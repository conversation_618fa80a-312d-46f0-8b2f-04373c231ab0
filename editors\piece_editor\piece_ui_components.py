"""
Piece Editor UI Components for Adventure Chess Creator

This module contains all UI creation and layout logic for the piece editor:
- Main UI initialization and layout
- Widget creation and styling
- Form sections and grouping
- Responsive layout handling
- Button creation and configuration

Extracted from piece_editor.py to improve maintainability and separate
UI concerns from business logic.
"""

import logging
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTextEdit, QPushButton,
    QComboBox, QGroupBox, QSpinBox, QCheckBox, QScrollArea, QGridLayout,
    QFormLayout, QListWidget, QSizePolicy, QTabWidget
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QWheelEvent, QKeyEvent, QFont

# Local imports
from config import PIECE_ROLES, RECHARGE_TYPES
from core.ui import ResponsiveLayout, ResponsiveScrollArea, make_widget_responsive
from core.ui import ValidationStatusWidget
from core.ui import InlineAbilitySelector

logger = logging.getLogger(__name__)


class ScrollableComboBox(QComboBox):
    """
    Enhanced QComboBox with mouse wheel and arrow key scroll support.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFocusPolicy(Qt.FocusPolicy.WheelFocus)

    def wheelEvent(self, event: QWheelEvent):
        """Handle mouse wheel events for scrolling through items."""
        if self.hasFocus():
            # Get the current index
            current_index = self.currentIndex()

            # Determine scroll direction
            if event.angleDelta().y() > 0:  # Scroll up
                new_index = max(0, current_index - 1)
            else:  # Scroll down
                new_index = min(self.count() - 1, current_index + 1)

            # Set new index if it changed
            if new_index != current_index:
                self.setCurrentIndex(new_index)
        else:
            # If not focused, pass event to parent
            super().wheelEvent(event)

    def keyPressEvent(self, event: QKeyEvent):
        """Handle arrow key events for navigation."""
        if event.key() in (Qt.Key.Key_Up, Qt.Key.Key_Down):
            current_index = self.currentIndex()

            if event.key() == Qt.Key.Key_Up:
                new_index = max(0, current_index - 1)
            else:  # Key_Down
                new_index = min(self.count() - 1, current_index + 1)

            if new_index != current_index:
                self.setCurrentIndex(new_index)
        else:
            super().keyPressEvent(event)


class PieceUIComponents:
    """
    Handles all UI component creation and layout for the piece editor.
    
    This class separates UI creation from business logic, making the code
    more maintainable and easier to modify.
    """
    
    def __init__(self, editor_instance):
        """
        Initialize UI components handler.
        
        Args:
            editor_instance: The PieceEditorWindow instance
        """
        self.editor = editor_instance
    
    def init_main_ui(self) -> QWidget:
        """
        Initialize the main user interface with tabs.

        Returns:
            The central widget for the main window
        """
        try:
            from PyQt6.QtWidgets import QTabWidget
            from core.ui import TabWidgetResponsive, make_widget_responsive

            main_layout = QVBoxLayout()

            # Top toolbar with file operations
            toolbar_layout = self._create_toolbar()
            main_layout.addLayout(toolbar_layout)

            # Main content in tabs
            self.editor.tab_widget = self._create_tab_widget()
            main_layout.addWidget(self.editor.tab_widget)

            # Status display
            self.editor.status_widget = ValidationStatusWidget()
            main_layout.addWidget(self.editor.status_widget)

            # Keep status_label for backward compatibility
            self.editor.status_label = self.editor.status_widget.status_label

            # Create central widget
            central_widget = QWidget()
            central_widget.setLayout(main_layout)

            logger.info("Main UI initialized successfully")
            return central_widget

        except Exception as e:
            logger.error(f"Error initializing main UI: {e}")
            raise

    def _create_tab_widget(self) -> QTabWidget:
        """
        Create the main tab widget with all tabs.

        Returns:
            The configured tab widget
        """
        from PyQt6.QtWidgets import QTabWidget
        from core.ui import TabWidgetResponsive, make_widget_responsive

        tab_widget = QTabWidget()
        TabWidgetResponsive.setup_tab_widget(tab_widget)
        make_widget_responsive(tab_widget)

        # Basic Info Tab
        basic_tab = self.create_basic_tab()
        tab_widget.addTab(basic_tab, "Basic Info")

        # Movement Tab
        movement_tab = self.create_movement_tab()
        tab_widget.addTab(movement_tab, "Movement/Recharge")

        # Abilities Tab
        abilities_tab = self.create_abilities_tab()
        tab_widget.addTab(abilities_tab, "Abilities")

        # Preview Tab
        preview_tab = self.create_preview_tab()
        tab_widget.addTab(preview_tab, "Preview")

        return tab_widget

    def create_basic_tab(self) -> QWidget:
        """Create the basic info tab."""
        try:
            # Create scrollable content
            scroll_area = ResponsiveScrollArea()
            main_layout = scroll_area.content_layout

            # Basic piece information section
            basic_info_section = self._create_basic_info_section()
            main_layout.addWidget(basic_info_section)

            # Add stretch to ensure proper scrolling behavior
            main_layout.addStretch()

            logger.debug("Basic tab created successfully")
            return scroll_area

        except Exception as e:
            logger.error(f"Error creating basic tab: {e}")
            raise

    def create_movement_tab(self) -> QWidget:
        """Create the movement tab."""
        try:
            # Create scrollable content
            scroll_area = ResponsiveScrollArea()
            main_layout = scroll_area.content_layout

            # Movement section
            movement_section = self._create_movement_section()
            main_layout.addWidget(movement_section)

            # Add stretch to ensure proper scrolling behavior
            main_layout.addStretch()

            logger.debug("Movement tab created successfully")
            return scroll_area

        except Exception as e:
            logger.error(f"Error creating movement tab: {e}")
            raise

    def create_abilities_tab(self) -> QWidget:
        """Create the abilities tab."""
        try:
            # Create scrollable content
            scroll_area = ResponsiveScrollArea()
            main_layout = scroll_area.content_layout

            # Abilities section
            abilities_section = self._create_abilities_section()
            main_layout.addWidget(abilities_section)

            # Promotion section (as expandable card at bottom)
            self.editor.promotion_section = self._create_promotion_section()
            self.editor.promotion_section.setVisible(False)  # Initially hidden
            main_layout.addWidget(self.editor.promotion_section)

            # Console log
            console_section = self._create_console_section()
            main_layout.addWidget(console_section)

            # Add stretch to ensure proper scrolling behavior
            main_layout.addStretch()

            logger.debug("Abilities tab created successfully")
            return scroll_area

        except Exception as e:
            logger.error(f"Error creating abilities tab: {e}")
            raise

    def create_preview_tab(self) -> QWidget:
        """
        Create the preview tab showing JSON representation.

        Returns:
            The preview tab widget
        """
        try:
            import json

            # Create main widget
            preview_widget = QWidget()
            layout = QVBoxLayout()

            # Header with refresh button
            header_layout = QHBoxLayout()
            header_label = QLabel("JSON Preview")
            header_label.setStyleSheet("QLabel { font-weight: bold; font-size: 14px; }")

            refresh_btn = QPushButton("Refresh Preview")
            refresh_btn.clicked.connect(self.refresh_preview)

            header_layout.addWidget(header_label)
            header_layout.addStretch()
            header_layout.addWidget(refresh_btn)
            layout.addLayout(header_layout)

            # JSON display area
            self.editor.preview_text = QTextEdit()
            self.editor.preview_text.setReadOnly(True)
            self.editor.preview_text.setFont(QFont("Consolas", 10))
            self.editor.preview_text.setStyleSheet("""
                QTextEdit {
                    background-color: #2b2b2b;
                    color: #ffffff;
                    border: 1px solid #555;
                    border-radius: 4px;
                }
            """)
            layout.addWidget(self.editor.preview_text)

            preview_widget.setLayout(layout)

            # Initial preview
            self.refresh_preview()

            logger.debug("Preview tab created successfully")
            return preview_widget

        except Exception as e:
            logger.error(f"Error creating preview tab: {e}")
            raise

    def refresh_preview(self):
        """Refresh the JSON preview with current form data."""
        try:
            import json

            # Collect current form data
            data = self.editor.collect_form_data()

            # Format as pretty JSON
            json_str = json.dumps(data, indent=2, ensure_ascii=False)

            # Update preview
            if hasattr(self.editor, 'preview_text'):
                self.editor.preview_text.setPlainText(json_str)

        except Exception as e:
            logger.error(f"Error refreshing preview: {e}")
            if hasattr(self.editor, 'preview_text'):
                self.editor.preview_text.setPlainText(f"Error generating preview: {e}")
    
    def _create_toolbar(self) -> QHBoxLayout:
        """
        Create the top toolbar with file operations.
        
        Returns:
            The toolbar layout
        """
        toolbar_layout = QHBoxLayout()
        
        # Standardized file operations buttons in specified order: New → Save → Delete → Load-Quick

        # 1. New button
        self.editor.new_btn = QPushButton("📄 New Piece")
        self.editor.new_btn.clicked.connect(self.editor.new_piece)
        self.editor.new_btn.setToolTip("Create a new piece")
        toolbar_layout.addWidget(self.editor.new_btn)

        # 2. Save button
        self.editor.save_btn = QPushButton("💾 Save Piece")
        self.editor.save_btn.clicked.connect(self.editor.save_piece)
        self.editor.save_btn.setToolTip("Save the current piece")
        toolbar_layout.addWidget(self.editor.save_btn)

        # 3. Delete button
        self.editor.delete_btn = QPushButton("🗑️ Delete Piece")
        self.editor.delete_btn.clicked.connect(self.editor.delete_current_piece)
        self.editor.delete_btn.setToolTip("Delete the currently loaded piece file")
        # Style the delete button to indicate destructive action
        self.editor.delete_btn.setStyleSheet("""
            QPushButton {
                color: #d32f2f;
                font-weight: bold;
                padding: 8px 12px;
                border-radius: 4px;
                border: 1px solid palette(mid);
                background-color: palette(button);
            }
            QPushButton:hover {
                background-color: #ffebee;
                border-color: #d32f2f;
                color: #b71c1c;
            }
        """)
        toolbar_layout.addWidget(self.editor.delete_btn)

        # Separator
        toolbar_layout.addSpacing(20)

        # 4. Load-Quick dropdown with auto-refresh and scroll support
        load_layout = QHBoxLayout()
        load_layout.addWidget(QLabel("Load-Quick:"))

        self.editor.quick_load_combo = ScrollableComboBox()
        self.editor.quick_load_combo.addItem("Select piece to load...")
        self.editor.quick_load_combo.currentTextChanged.connect(
            self.editor.on_quick_load_selection
        )
        load_layout.addWidget(self.editor.quick_load_combo)
        
        toolbar_layout.addLayout(load_layout)
        toolbar_layout.addStretch()
        
        return toolbar_layout
    
    def _create_basic_info_section(self) -> QGroupBox:
        """
        Create the basic piece information section with icon selection on the right.

        Returns:
            The basic info group box
        """
        group = QGroupBox("Basic Piece Information")
        main_layout = QHBoxLayout()

        # Left side: Basic information
        left_layout = QFormLayout()

        # Name field
        self.editor.name_edit = QLineEdit()
        self.editor.name_edit.setPlaceholderText("Enter piece name...")
        left_layout.addRow("Name:", self.editor.name_edit)

        # Description field
        self.editor.description_edit = QTextEdit()
        self.editor.description_edit.setPlaceholderText("Enter piece description...")
        self.editor.description_edit.setMaximumHeight(80)
        left_layout.addRow("Description:", self.editor.description_edit)

        # Role selection
        self.editor.role_combo = QComboBox()
        for role_name, _ in PIECE_ROLES:
            self.editor.role_combo.addItem(role_name)
        self.editor.role_combo.currentTextChanged.connect(self.editor.on_role_changed)
        left_layout.addRow("Role:", self.editor.role_combo)

        # Checkboxes for piece properties
        checkbox_layout = QHBoxLayout()

        self.editor.can_castle_check = QCheckBox("Can Castle")
        checkbox_layout.addWidget(self.editor.can_castle_check)

        self.editor.color_directional_check = QCheckBox("Color Directional")
        checkbox_layout.addWidget(self.editor.color_directional_check)

        checkbox_layout.addStretch()
        left_layout.addRow("Properties:", checkbox_layout)

        # Enable Promotions checkbox (separate row for visibility)
        self.editor.enable_promotions_check = QCheckBox("Enable Promotions")
        self.editor.enable_promotions_check.toggled.connect(self.editor.update_promotion_visibility)
        left_layout.addRow("", self.editor.enable_promotions_check)

        # Create left widget
        left_widget = QWidget()
        left_widget.setLayout(left_layout)
        main_layout.addWidget(left_widget)

        # Right side: Icon selection
        right_layout = QVBoxLayout()
        icon_label = QLabel("Piece Icons")
        icon_label.setStyleSheet("font-weight: bold; color: #333; margin-bottom: 5px;")
        right_layout.addWidget(icon_label)

        # Icon selection will be handled by icon manager
        self.editor.icon_manager.create_icon_ui(right_layout)

        right_layout.addStretch()

        # Create right widget with fixed width
        right_widget = QWidget()
        right_widget.setLayout(right_layout)
        right_widget.setMaximumWidth(300)
        main_layout.addWidget(right_widget)

        group.setLayout(main_layout)
        return group
    
    def _create_movement_section(self) -> QGroupBox:
        """
        Create the movement pattern section with compact two-column layout.

        Returns:
            The movement group box
        """
        group = QGroupBox("Movement Pattern")
        main_layout = QHBoxLayout()

        # Left side: Movement type selection
        left_layout = QVBoxLayout()
        left_layout.addWidget(QLabel("Movement Type:"))

        # Quick pattern buttons with chess piece symbols (arranged in grid)
        self.editor.movement_pattern_buttons = []
        movement_buttons = [
            ("♜", "orthogonal", "Orthogonal lines (like Rook)"),
            ("♝", "diagonal", "Diagonal lines (like Bishop)"),
            ("♛", "any", "All directions (like Queen)"),
            ("♞", "lShape", "L-shaped moves (like Knight)"),
            ("♚", "king", "King movement (1 square any direction)"),
            ("🌐", "global", "Global movement (anywhere on board)"),
            ("🎨", "custom", "Custom pattern (opens pattern editor)")
        ]

        # Create grid layout for buttons (3 rows, 3 columns max)
        button_grid_layout = QGridLayout()
        button_grid_layout.setSpacing(3)

        for i, (symbol, movement_type, tooltip) in enumerate(movement_buttons):
            btn = QPushButton(symbol)
            btn.setFixedSize(35, 30)
            btn.setToolTip(f"{tooltip}")
            btn.setCheckable(True)
            btn.setStyleSheet(self._get_movement_button_style())
            btn.clicked.connect(lambda checked, mt=movement_type: self.editor.on_movement_pattern_selected(mt))

            self.editor.movement_pattern_buttons.append((btn, movement_type))

            # Arrange in 3 columns
            row = i // 3
            col = i % 3
            button_grid_layout.addWidget(btn, row, col)

        # Create widget for button grid
        button_grid_widget = QWidget()
        button_grid_widget.setLayout(button_grid_layout)
        left_layout.addWidget(button_grid_widget)
        left_layout.addStretch()

        # Create left widget
        left_widget = QWidget()
        left_widget.setLayout(left_layout)
        left_widget.setMaximumWidth(150)
        main_layout.addWidget(left_widget)

        # Middle: Compact pattern preview
        middle_layout = QVBoxLayout()
        preview_label = QLabel("Pattern Preview:")
        preview_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        middle_layout.addWidget(preview_label)

        # Create compact 8x8 grid for live pattern preview
        preview_grid_layout = QGridLayout()
        preview_grid_layout.setSpacing(1)

        # Initialize preview grid buttons with pattern editor styling (smaller size)
        self.editor.preview_grid_buttons = []
        for row in range(8):
            button_row = []
            for col in range(8):
                btn = QPushButton()
                btn.setFixedSize(20, 20)  # Reduced from 25x25 to 20x20
                btn.setEnabled(False)  # Preview only, not interactive
                # Apply pattern editor default styling (empty square)
                btn.setStyleSheet("background: #2d3748; border: 1px solid #4a5568; font-size: 7px; font-weight: bold;")
                preview_grid_layout.addWidget(btn, row, col)
                button_row.append(btn)
            self.editor.preview_grid_buttons.append(button_row)

        # Create container widget for the grid
        preview_grid_widget = QWidget()
        preview_grid_widget.setLayout(preview_grid_layout)
        preview_grid_widget.setMaximumSize(180, 180)  # Reduced from 220x220

        middle_layout.addWidget(preview_grid_widget)
        middle_layout.addStretch()

        # Create middle widget
        middle_widget = QWidget()
        middle_widget.setLayout(middle_layout)
        main_layout.addWidget(middle_widget)

        # Right side: Complete Points and Recharge system
        right_widget = self._create_points_recharge_subsection()
        main_layout.addWidget(right_widget)

        group.setLayout(main_layout)
        return group
    
    def _create_abilities_section(self) -> QGroupBox:
        """
        Create the abilities section.

        Returns:
            The abilities group box
        """
        group = QGroupBox("Piece Abilities")
        abilities_layout = QVBoxLayout()

        # Instructions
        instructions = QLabel(
            "Select abilities for this piece. Use the inline selector to add/remove abilities."
        )
        instructions.setWordWrap(True)
        instructions.setStyleSheet("color: #666; font-style: italic; margin-bottom: 10px;")
        abilities_layout.addWidget(instructions)

        # Use the proper inline ability selector widget
        self.editor.ability_selector = InlineAbilitySelector(self.editor, "Piece Abilities")
        self.editor.ability_selector.abilities_changed.connect(self.editor.on_abilities_changed)
        abilities_layout.addWidget(self.editor.ability_selector)

        # Quick action buttons removed per user request

        group.setLayout(abilities_layout)
        return group
    
    def _create_promotion_section(self) -> QGroupBox:
        """
        Create the simplified promotion section.

        Returns:
            The promotion group box
        """
        group = QGroupBox("Promotion Settings")
        main_layout = QHBoxLayout()

        # Left side: Controls
        controls_layout = QVBoxLayout()

        # Promotion type selection
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("Promotion Type:"))
        self.editor.promotion_type_combo = QComboBox()
        self.editor.promotion_type_combo.addItems(["Primary Promotions", "Secondary Promotions"])
        self.editor.promotion_type_combo.setToolTip("Primary: When piece reaches enemy back rank\nSecondary: When promoted piece reaches friendly back rank")
        type_layout.addWidget(self.editor.promotion_type_combo)
        type_layout.addStretch()
        controls_layout.addLayout(type_layout)

        # Piece selection
        piece_layout = QHBoxLayout()
        piece_layout.addWidget(QLabel("Select Piece:"))
        self.editor.promotion_piece_combo = QComboBox()
        self.editor.promotion_piece_combo.addItem("Choose piece to add...")
        piece_layout.addWidget(self.editor.promotion_piece_combo)

        # Add button
        self.editor.add_promotion_btn = QPushButton("Add")
        self.editor.add_promotion_btn.setFixedWidth(60)
        self.editor.add_promotion_btn.clicked.connect(self.editor.add_promotion_piece)
        piece_layout.addWidget(self.editor.add_promotion_btn)

        controls_layout.addLayout(piece_layout)
        controls_layout.addStretch()

        main_layout.addLayout(controls_layout)

        # Right side: Display area
        display_layout = QVBoxLayout()

        # Primary promotions display
        primary_label = QLabel("Primary Promotions:")
        primary_label.setStyleSheet("font-weight: bold; color: #333;")
        display_layout.addWidget(primary_label)

        self.editor.primary_promo_list = QListWidget()
        self.editor.primary_promo_list.setMaximumHeight(80)
        self.editor.primary_promo_list.setStyleSheet("""
            QListWidget {
                background-color: palette(base);
                border: 1px solid palette(mid);
                border-radius: 3px;
                padding: 2px;
            }
            QListWidget::item {
                padding: 2px 5px;
                border-bottom: 1px solid palette(mid);
            }
            QListWidget::item:hover {
                background-color: palette(highlight);
                color: palette(highlighted-text);
            }
        """)
        display_layout.addWidget(self.editor.primary_promo_list)

        # Secondary promotions display
        secondary_label = QLabel("Secondary Promotions:")
        secondary_label.setStyleSheet("font-weight: bold; color: #333;")
        display_layout.addWidget(secondary_label)

        self.editor.secondary_promo_list = QListWidget()
        self.editor.secondary_promo_list.setMaximumHeight(80)
        self.editor.secondary_promo_list.setStyleSheet("""
            QListWidget {
                background-color: palette(base);
                border: 1px solid palette(mid);
                border-radius: 3px;
                padding: 2px;
            }
            QListWidget::item {
                padding: 2px 5px;
                border-bottom: 1px solid palette(mid);
            }
            QListWidget::item:hover {
                background-color: palette(highlight);
                color: palette(highlighted-text);
            }
        """)
        display_layout.addWidget(self.editor.secondary_promo_list)

        main_layout.addLayout(display_layout)

        group.setLayout(main_layout)
        return group
    


    def _create_points_recharge_subsection(self) -> QWidget:
        """
        Create the complete points and recharge subsection for the movement section.

        Returns:
            The points and recharge widget
        """
        widget = QWidget()
        layout = QVBoxLayout()

        # Master enable checkbox for entire section (default unchecked)
        self.editor.enable_points_recharge_check = QCheckBox("Enable Points and Recharge System")
        self.editor.enable_points_recharge_check.setChecked(False)  # Default unchecked state
        self.editor.enable_points_recharge_check.toggled.connect(self.editor.update_points_recharge_options)
        layout.addWidget(self.editor.enable_points_recharge_check)

        # Form layout for all points and recharge controls
        form_layout = QFormLayout()

        # Points section
        points_label = QLabel("Points Configuration")
        points_label.setStyleSheet("font-weight: bold; margin-top: 10px; margin-bottom: 5px;")
        form_layout.addRow("", points_label)

        # Max points (default value = 1)
        self.editor.max_points_spin = QSpinBox()
        self.editor.max_points_spin.setRange(0, 100)
        self.editor.max_points_spin.setValue(1)  # Set default to 1
        form_layout.addRow("Max Points:", self.editor.max_points_spin)

        # Starting points (default value = 1)
        self.editor.starting_points_spin = QSpinBox()
        self.editor.starting_points_spin.setRange(0, 100)
        self.editor.starting_points_spin.setValue(1)  # Set default to 1
        form_layout.addRow("Starting Points:", self.editor.starting_points_spin)

        # Recharge section
        recharge_label = QLabel("Recharge Configuration")
        recharge_label.setStyleSheet("font-weight: bold; margin-top: 10px; margin-bottom: 5px;")
        form_layout.addRow("", recharge_label)

        # Recharge type
        self.editor.recharge_type_combo = QComboBox()
        for recharge_type, _ in RECHARGE_TYPES:
            self.editor.recharge_type_combo.addItem(recharge_type)
        self.editor.recharge_type_combo.currentTextChanged.connect(self.editor.update_recharge_options)
        form_layout.addRow("Type:", self.editor.recharge_type_combo)

        # Turn recharge spinner
        self.editor.turn_recharge_spin = QSpinBox()
        self.editor.turn_recharge_spin.setRange(1, 10)
        self.editor.turn_recharge_spin.setValue(1)
        self.editor.turn_recharge_spin.setToolTip("Points gained per turn")
        form_layout.addRow("Per Turn:", self.editor.turn_recharge_spin)

        # Adjacency configuration button (using unified dialog)
        self.editor.adjacency_config_btn = QPushButton("Configure...")
        self.editor.adjacency_config_btn.clicked.connect(self.editor.open_adjacency_required_dialog)
        self.editor.adjacency_config_btn.setToolTip("Configure which pieces must be adjacent for recharge")
        form_layout.addRow("Adjacency:", self.editor.adjacency_config_btn)

        # Committed turns spinner
        self.editor.committed_turns_spin = QSpinBox()
        self.editor.committed_turns_spin.setRange(1, 10)
        self.editor.committed_turns_spin.setValue(1)
        self.editor.committed_turns_spin.setToolTip("Number of turns piece must commit to recharge")
        form_layout.addRow("Committed:", self.editor.committed_turns_spin)

        layout.addLayout(form_layout)
        layout.addStretch()

        # Store references for enabling/disabling - include ALL points and recharge controls
        self.editor.points_recharge_widgets = [
            self.editor.max_points_spin,
            self.editor.starting_points_spin,
            self.editor.recharge_type_combo,
            self.editor.turn_recharge_spin,
            self.editor.adjacency_config_btn,
            self.editor.committed_turns_spin
        ]

        # Store labels for graying out
        self.editor.points_recharge_labels = [points_label, recharge_label]

        widget.setLayout(layout)
        widget.setMaximumWidth(250)  # Slightly wider to accommodate all controls
        return widget
    

    def _create_console_section(self) -> QGroupBox:
        """
        Create the console log section.
        
        Returns:
            The console group box
        """
        group = QGroupBox("Console Log")
        layout = QVBoxLayout()
        
        self.editor.log_console = QTextEdit()
        self.editor.log_console.setMaximumHeight(100)
        self.editor.log_console.setReadOnly(True)
        self.editor.log_console.setStyleSheet(
            "background-color: #2b2b2b; color: #ffffff; font-family: 'Courier New', monospace; font-size: 10px;"
        )
        layout.addWidget(self.editor.log_console)
        
        group.setLayout(layout)
        return group
    
    def _get_movement_button_style(self) -> str:
        """
        Get the CSS style for movement pattern buttons.
        
        Returns:
            CSS style string
        """
        return """
            QPushButton {
                font-size: 16px;
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 6px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #3a3a3a, stop:1 #2a2a2a);
                color: white;
                padding: 2px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #4a4a4a, stop:1 #3a3a3a);
                border-color: #66aaff;
            }
            QPushButton:checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #4488cc, stop:1 #3366aa);
                border-color: #66aaff;
                border-width: 3px;
            }
        """
