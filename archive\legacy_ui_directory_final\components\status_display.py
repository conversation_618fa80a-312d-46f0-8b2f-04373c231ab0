"""
Status Display Components for Adventure Chess Creator

This module contains status and validation UI components extracted from ui_shared_components.py:
- ValidationStatusWidget: Reusable validation status display with color coding

This component provides consistent status feedback across all editors
in the application.
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel


class ValidationStatusWidget(QWidget):
    """
    Reusable validation status display
    Shows validation results with color coding
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the validation status UI"""
        layout = QVBoxLayout()
        
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
                background-color: palette(base);
                color: palette(text);
                border: 1px solid palette(mid);
            }
        """)
        
        layout.addWidget(self.status_label)
        self.setLayout(layout)
    
    def show_success(self, message: str):
        """Show success status"""
        self.status_label.setText(f"✅ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
        """)
    
    def show_error(self, message: str):
        """Show error status"""
        self.status_label.setText(f"❌ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
        """)
    
    def show_warning(self, message: str):
        """Show warning status"""
        self.status_label.setText(f"⚠️ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
                background-color: #fff3cd;
                color: #856404;
                border: 1px solid #ffeaa7;
            }
        """)
    
    def show_info(self, message: str):
        """Show info status"""
        self.status_label.setText(f"ℹ️ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
                background-color: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
            }
        """)
