"""
Core Performance Module for Adventure Chess Creator

This module contains performance optimization and lazy loading systems.
Consolidated from the enhancements/performance/ folder for better organization.
"""

# Lazy Loading System
from .lazy_loading_system import (
    <PERSON>zyDataManager,
    LazyFileListManager,
    LazyIntegratedDataManager,
    LazyComboBox,
    LazyListWidget,
    LazyLoadingStatusWidget,
    LazyFileSelector,
    LoadingProgressWidget,
    get_lazy_manager,
    get_lazy_data_manager,
    reset_lazy_manager
)

# File System Optimization
from .file_system_optimization import (
    FileSystemOptimizer,
    FileIndexEntry,
    SearchResult,
    OptimizedDataManager,
    get_file_system_optimizer,
    get_optimized_data_manager,
    reset_optimizer,
    reset_optimized_data_manager
)

# Editor Integration and Cache Management
from .editor_integration import (
    EnhancedCacheManager,
    CacheEntry,
    LazyPieceEditorPatches,
    LazyAbilityEditorPatches,
    apply_lazy_loading_patches,
    create_lazy_file_selector_widget,
    get_cache_manager
)

__all__ = [
    # Lazy Loading System
    'LazyDataManager',
    'LazyFileListManager',
    'LazyIntegratedDataManager',
    'LazyComboBox',
    'LazyListWidget',
    'LazyLoadingStatusWidget',
    'LazyFileSelector',
    'LoadingProgressWidget',
    'get_lazy_manager',
    'get_lazy_data_manager',
    'reset_lazy_manager',

    # File System Optimization
    'FileSystemOptimizer',
    'FileIndexEntry',
    'SearchResult',
    'OptimizedDataManager',
    'get_file_system_optimizer',
    'get_optimized_data_manager',
    'reset_optimizer',
    'reset_optimized_data_manager',

    # Editor Integration and Cache Management
    'EnhancedCacheManager',
    'CacheEntry',
    'LazyPieceEditorPatches',
    'LazyAbilityEditorPatches',
    'apply_lazy_loading_patches',
    'create_lazy_file_selector_widget',
    'get_cache_manager'
]
