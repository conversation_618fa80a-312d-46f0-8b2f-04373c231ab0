"""
Core UI Module for Adventure Chess Creator

This module contains core UI components and visual feedback systems.
Consolidated from the enhancements/ui/ folder and ui/ directory for better organization.
"""

# Visual feedback components (from enhancements)
from .color_schemes import ColorSchemes
from .loading_indicators import EnhancedLoadingIndicator, OperationFeedbackManager
from .grid_visualization import EnhancedGridVisualization, GridPatternEditor
from .visual_feedback_integration import (
    VisualFeedbackIntegrator, VisualFeedbackManager,
    get_visual_feedback_manager, apply_visual_feedback_to_editor, apply_visual_feedback_to_main_window
)

# UI components (from ui/ directory)
from .grid_components import GridToggleWidget, AreaEffectGridWidget
from .file_operations import FileOperationsWidget
from .status_display import ValidationStatusWidget
from .ui_utilities import (
    create_section_header,
    create_info_box,
    create_legend_item,
    create_dialog_buttons,
    create_grid_instructions
)
from .inline_selection_widgets import InlinePieceSelector, InlineAbilitySelector
from .adjacency_preview import AdjacencyPreviewWidget
from .range_preview import RangePreviewWidget
from .ui_utils import (
    ResponsiveScrollArea, ResponsiveWidget, ResponsiveLayout, ResponsiveSplitter,
    TabWidgetResponsive, setup_responsive_window, get_screen_size_category,
    setup_responsive_window_with_fallback, make_widget_responsive,
    create_scrollable_content, optimize_layout_for_small_screens
)

# UI components (from enhancements integration)
from .ui_components import (
    # Search components
    SearchWorker,
    EnhancedSearchWidget,
    SearchResultsWidget,
    FileIndexBrowser,

    # Validation components
    ValidationRules,
    EnhancedValidationMixin,
    create_piece_validation_rules,
    create_ability_validation_rules,
)

__all__ = [
    # Visual feedback components
    'ColorSchemes',
    'EnhancedLoadingIndicator',
    'OperationFeedbackManager',
    'EnhancedGridVisualization',
    'GridPatternEditor',
    'VisualFeedbackIntegrator',
    'VisualFeedbackManager',
    'get_visual_feedback_manager',
    'apply_visual_feedback_to_editor',
    'apply_visual_feedback_to_main_window',

    # Grid components
    'GridToggleWidget',
    'AreaEffectGridWidget',

    # File operations
    'FileOperationsWidget',

    # Status display
    'ValidationStatusWidget',

    # Utility functions
    'create_section_header',
    'create_info_box',
    'create_legend_item',
    'create_dialog_buttons',
    'create_grid_instructions',

    # Inline selection widgets
    'InlinePieceSelector',
    'InlineAbilitySelector',

    # Adjacency preview
    'AdjacencyPreviewWidget',
    'RangePreviewWidget',

    # Responsive UI utilities
    'ResponsiveScrollArea',
    'ResponsiveWidget',
    'ResponsiveLayout',
    'ResponsiveSplitter',
    'TabWidgetResponsive',
    'setup_responsive_window',
    'get_screen_size_category',
    'setup_responsive_window_with_fallback',
    'make_widget_responsive',
    'create_scrollable_content',
    'optimize_layout_for_small_screens',

    # Search components
    'SearchWorker',
    'EnhancedSearchWidget',
    'SearchResultsWidget',
    'FileIndexBrowser',

    # Validation components
    'ValidationRules',
    'EnhancedValidationMixin',
    'create_piece_validation_rules',
    'create_ability_validation_rules',
]
