"""
Core UI Module for Adventure Chess Creator

This module contains core UI components and visual feedback systems.
Consolidated from the enhancements/ui/ folder for better organization.
"""

from .color_schemes import ColorSchemes
from .loading_indicators import EnhancedLoadingIndicator, OperationFeedbackManager
from .grid_visualization import EnhancedGridVisualization, GridPatternEditor
from .visual_feedback_integration import (
    VisualFeedbackIntegrator, VisualFeedbackManager,
    get_visual_feedback_manager, apply_visual_feedback_to_editor, apply_visual_feedback_to_main_window
)

__all__ = [
    'ColorSchemes',
    'EnhancedLoadingIndicator',
    'OperationFeedbackManager',
    'EnhancedGridVisualization',
    'GridPatternEditor',
    'VisualFeedbackIntegrator',
    'VisualFeedbackManager',
    'get_visual_feedback_manager',
    'apply_visual_feedback_to_editor',
    'apply_visual_feedback_to_main_window'
]
