"""
Visual Feedback Integration for Adventure Chess Creator

This module provides integration utilities for applying visual feedback enhancements
to existing editors and widgets throughout the application.
"""

import logging
from typing import List, Optional, Dict, Any
from PyQt6.QtCore import QTimer
from PyQt6.QtWidgets import QWidget

from .loading_indicators import <PERSON>hanced<PERSON>oadingIndicator, OperationFeedbackManager
from ..validation.validation_widgets import RealTimeValidationWidget
from .grid_visualization import EnhancedGridVisualization

logger = logging.getLogger(__name__)


class VisualFeedbackIntegrator:
    """Integrates visual feedback enhancements into existing editors"""

    @staticmethod
    def enhance_editor(editor, enable_validation: bool = True, enable_operations: bool = True):
        """
        Enhance an editor with visual feedback components

        Args:
            editor: The editor widget to enhance
            enable_validation: Whether to add real-time validation
            enable_operations: Whether to add operation feedback
        """
        # Add loading indicator
        if not hasattr(editor, 'loading_indicator'):
            editor.loading_indicator = EnhancedLoadingIndicator(editor)
            # For QMainWindow, add to central widget's layout instead of main layout
            target_layout = None
            if hasattr(editor, 'centralWidget') and editor.centralWidget():
                central_widget = editor.centralWidget()
                if hasattr(central_widget, 'layout') and central_widget.layout():
                    target_layout = central_widget.layout()
            elif hasattr(editor, 'layout') and editor.layout():
                target_layout = editor.layout()

            if target_layout:
                target_layout.addWidget(editor.loading_indicator)

        # Add validation widget
        if enable_validation and not hasattr(editor, 'validation_widget'):
            editor.validation_widget = RealTimeValidationWidget(editor)
            # For QMainWindow, add to central widget's layout instead of main layout
            target_layout = None
            if hasattr(editor, 'centralWidget') and editor.centralWidget():
                central_widget = editor.centralWidget()
                if hasattr(central_widget, 'layout') and central_widget.layout():
                    target_layout = central_widget.layout()
            elif hasattr(editor, 'layout') and editor.layout():
                target_layout = editor.layout()

            if target_layout:
                target_layout.addWidget(editor.validation_widget)

        # Add operation feedback
        if enable_operations and not hasattr(editor, 'operation_manager'):
            editor.operation_manager = OperationFeedbackManager(editor)
            editor.operation_manager.register_feedback_widget('default', editor.loading_indicator)

        logger.info(f"Visual feedback enhancements applied to {editor.__class__.__name__}")

    @staticmethod
    def add_validation_rules(editor, rules: List[tuple]):
        """
        Add validation rules to an editor

        Args:
            editor: The editor widget
            rules: List of (rule_name, validator_function) tuples
        """
        if hasattr(editor, 'validation_widget'):
            for rule_name, validator in rules:
                editor.validation_widget.add_validation_rule(rule_name, validator)


class VisualFeedbackManager:
    """Central manager for all visual feedback enhancements"""

    def __init__(self):
        self.enhanced_editors = {}
        self.operation_managers = {}
        self.validation_widgets = {}

    def enhance_piece_editor(self, piece_editor):
        """Enhance piece editor with visual feedback"""
        try:
            # Apply basic enhancements
            VisualFeedbackIntegrator.enhance_editor(
                piece_editor,
                enable_validation=True,
                enable_operations=True
            )

            # Add piece-specific validation rules
            validation_rules = self._create_piece_validation_rules()
            VisualFeedbackIntegrator.add_validation_rules(piece_editor, validation_rules)

            # Setup data change tracking
            self._setup_piece_editor_tracking(piece_editor)

            self.enhanced_editors['piece_editor'] = piece_editor
            logger.info("Piece editor enhanced with visual feedback")

        except Exception as e:
            logger.error(f"Error enhancing piece editor: {e}")

    def enhance_ability_editor(self, ability_editor):
        """Enhance ability editor with visual feedback"""
        try:
            # Apply basic enhancements
            VisualFeedbackIntegrator.enhance_editor(
                ability_editor,
                enable_validation=True,
                enable_operations=True
            )

            # Add ability-specific validation rules
            validation_rules = self._create_ability_validation_rules()
            VisualFeedbackIntegrator.add_validation_rules(ability_editor, validation_rules)

            # Setup data change tracking
            self._setup_ability_editor_tracking(ability_editor)

            self.enhanced_editors['ability_editor'] = ability_editor
            logger.info("Ability editor enhanced with visual feedback")

        except Exception as e:
            logger.error(f"Error enhancing ability editor: {e}")

    def enhance_main_window(self, main_window):
        """Enhance main window with visual feedback"""
        try:
            # Apply basic visual feedback to main window
            # This could include status bar enhancements, global loading indicators, etc.
            self.enhanced_editors['main_window'] = main_window
            logger.info("Main window enhanced with visual feedback")

        except Exception as e:
            logger.error(f"Error enhancing main window: {e}")

    def _create_piece_validation_rules(self):
        """Create validation rules for piece editor"""
        def validate_piece_name(data):
            name = data.get('name', '').strip()
            if not name:
                return False, "Piece name is required"
            if len(name) < 2:
                return False, "Piece name must be at least 2 characters"
            return True, "Piece name is valid"

        def validate_movement_pattern(data):
            pattern = data.get('movement_pattern', [])
            if not pattern or not any(any(row) for row in pattern):
                return False, "Movement pattern cannot be empty"
            return True, "Movement pattern is valid"

        def validate_piece_value(data):
            value = data.get('value', 0)
            if value < 0:
                return False, "Piece value cannot be negative"
            if value > 1000:
                return False, "Piece value seems too high (max 1000)"
            return True, "Piece value is valid"

        return [
            ("Piece Name", validate_piece_name),
            ("Movement Pattern", validate_movement_pattern),
            ("Piece Value", validate_piece_value)
        ]

    def _create_ability_validation_rules(self):
        """Create validation rules for ability editor"""
        def validate_ability_name(data):
            name = data.get('name', '').strip()
            if not name:
                return False, "Ability name is required"
            if len(name) < 2:
                return False, "Ability name must be at least 2 characters"
            return True, "Ability name is valid"

        def validate_ability_cost(data):
            cost = data.get('cost', 0)
            if cost < 0:
                return False, "Ability cost cannot be negative"
            return True, "Ability cost is valid"

        def validate_ability_tags(data):
            tags = data.get('tags', [])
            if not tags:
                return False, "At least one ability tag is required"
            return True, "Ability tags are valid"

        return [
            ("Ability Name", validate_ability_name),
            ("Ability Cost", validate_ability_cost),
            ("Ability Tags", validate_ability_tags)
        ]

    def _setup_piece_editor_tracking(self, piece_editor):
        """Setup data change tracking for piece editor"""
        if hasattr(piece_editor, 'validation_widget'):
            # Connect form field changes to validation
            self._connect_piece_editor_signals(piece_editor)

    def _setup_ability_editor_tracking(self, ability_editor):
        """Setup data change tracking for ability editor"""
        if hasattr(ability_editor, 'validation_widget'):
            # Connect form field changes to validation
            self._connect_ability_editor_signals(ability_editor)

    def _connect_piece_editor_signals(self, piece_editor):
        """Connect piece editor signals to validation"""
        try:
            # Connect to common piece editor fields
            if hasattr(piece_editor, 'name_edit'):
                piece_editor.name_edit.textChanged.connect(
                    lambda: self._trigger_validation(piece_editor)
                )

            if hasattr(piece_editor, 'description_edit'):
                piece_editor.description_edit.textChanged.connect(
                    lambda: self._trigger_validation(piece_editor)
                )

            if hasattr(piece_editor, 'value_spin'):
                piece_editor.value_spin.valueChanged.connect(
                    lambda: self._trigger_validation(piece_editor)
                )

        except Exception as e:
            logger.error(f"Error connecting piece editor signals: {e}")

    def _connect_ability_editor_signals(self, ability_editor):
        """Connect ability editor signals to validation"""
        try:
            # Connect to common ability editor fields
            if hasattr(ability_editor, 'name_edit'):
                ability_editor.name_edit.textChanged.connect(
                    lambda: self._trigger_validation(ability_editor)
                )

            if hasattr(ability_editor, 'cost_spin'):
                ability_editor.cost_spin.valueChanged.connect(
                    lambda: self._trigger_validation(ability_editor)
                )

        except Exception as e:
            logger.error(f"Error connecting ability editor signals: {e}")

    def _trigger_validation(self, editor):
        """Trigger validation for an editor"""
        if hasattr(editor, 'validation_widget'):
            # Use a timer to debounce rapid changes
            if not hasattr(editor, '_validation_timer'):
                editor._validation_timer = QTimer()
                editor._validation_timer.setSingleShot(True)
                editor._validation_timer.timeout.connect(
                    lambda: editor.validation_widget.validate_all()
                )

            editor._validation_timer.start(300)  # 300ms delay

    def cleanup(self):
        """Cleanup all visual feedback enhancements"""
        try:
            # Cleanup operation managers
            for manager in self.operation_managers.values():
                if hasattr(manager, 'cleanup'):
                    manager.cleanup()

            # Clear references
            self.enhanced_editors.clear()
            self.operation_managers.clear()
            self.validation_widgets.clear()

            logger.info("Visual feedback manager cleaned up")

        except Exception as e:
            logger.error(f"Error during visual feedback cleanup: {e}")


# Global visual feedback manager instance
_visual_feedback_manager: Optional[VisualFeedbackManager] = None

def get_visual_feedback_manager() -> VisualFeedbackManager:
    """Get or create the global visual feedback manager"""
    global _visual_feedback_manager
    if _visual_feedback_manager is None:
        _visual_feedback_manager = VisualFeedbackManager()
    return _visual_feedback_manager

def apply_visual_feedback_to_editor(editor, editor_type: str = "piece"):
    """
    Apply visual feedback enhancements to an editor

    Args:
        editor: The editor instance to enhance
        editor_type: Type of editor ("piece" or "ability")
    """
    try:
        manager = get_visual_feedback_manager()

        if editor_type.lower() == "piece":
            manager.enhance_piece_editor(editor)
        elif editor_type.lower() == "ability":
            manager.enhance_ability_editor(editor)
        else:
            logger.warning(f"Unknown editor type: {editor_type}")

    except Exception as e:
        logger.error(f"Error applying visual feedback to editor: {e}")


def apply_visual_feedback_to_main_window(main_window):
    """
    Apply visual feedback enhancements to main window

    Args:
        main_window: The main window instance to enhance
    """
    try:
        manager = get_visual_feedback_manager()
        manager.enhance_main_window(main_window)

    except Exception as e:
        logger.error(f"Error applying visual feedback to main window: {e}")
