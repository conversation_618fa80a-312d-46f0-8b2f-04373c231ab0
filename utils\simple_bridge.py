#!/usr/bin/env python3
"""
Simple Bridge for Adventure Chess
Direct interface between editors and data storage without legacy validation
"""

import logging
from typing import Dict, Any, Optional, Tuple

from core.interfaces.editor_data_interface import EditorDataInterface
from schemas.data_manager import DirectDataManager

logger = logging.getLogger(__name__)


class SimpleBridge:
    """
    Simple bridge between editors and data storage
    No migration, no validation, just direct save/load
    """
    
    # ========== PIECE OPERATIONS ==========
    
    @staticmethod
    def get_piece_data_from_ui(editor) -> Dict[str, Any]:
        """
        Get piece data from UI components using EditorDataInterface
        """
        try:
            return EditorDataInterface.collect_data_from_ui(editor, "piece")
        except Exception as e:
            logger.error(f"Error getting piece data from UI: {e}")
            return {
                'version': '1.0.0',
                'name': '',
                'description': '',
                'role': 'Commander',
                'movement': {'type': 'orthogonal', 'pattern': None, 'piecePosition': [3, 3]},
                'canCapture': True,
                'abilities': [],
                'maxPoints': 0,
                'startingPoints': 0,
                'rechargeType': 'turnRecharge'
            }
    
    @staticmethod
    def set_piece_data_to_ui(editor, data: Dict[str, Any]):
        """
        Set piece data to UI components using EditorDataInterface
        """
        try:
            EditorDataInterface.populate_ui_from_data(editor, data, "piece")
            logger.info("Piece data set to UI successfully")
        except Exception as e:
            logger.error(f"Error setting piece data to UI: {e}")
    
    @staticmethod
    def load_piece_for_ui(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Load piece for UI consumption (returns dictionary)
        """
        return DirectDataManager.load_piece(filename)
    
    @staticmethod
    def save_piece_from_ui(editor, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save piece from UI data directly to JSON
        """
        try:
            # Get data from UI
            piece_data = SimpleBridge.get_piece_data_from_ui(editor)
            
            # Save directly
            return DirectDataManager.save_piece(piece_data, filename)
            
        except Exception as e:
            logger.error(f"Error saving piece from UI: {e}")
            return False, str(e)
    
    @staticmethod
    def list_pieces() -> list:
        """Get list of available pieces"""
        return DirectDataManager.list_pieces()
    
    @staticmethod
    def delete_piece(filename: str) -> Tuple[bool, Optional[str]]:
        """Delete a piece file"""
        return DirectDataManager.delete_piece(filename)
    
    # ========== ABILITY OPERATIONS ==========
    
    @staticmethod
    def get_ability_data_from_ui(editor) -> Dict[str, Any]:
        """
        Get ability data from UI components using EditorDataInterface
        """
        try:
            return EditorDataInterface.collect_data_from_ui(editor, "ability")
        except Exception as e:
            logger.error(f"Error getting ability data from UI: {e}")
            return {
                'version': '1.0.0',
                'name': '',
                'description': '',
                'cost': 0,
                'tags': []
            }
    
    @staticmethod
    def set_ability_data_to_ui(editor, data: Dict[str, Any]):
        """
        Set ability data to UI components using EditorDataInterface
        """
        try:
            EditorDataInterface.populate_ui_from_data(editor, data, "ability")
            logger.info("Ability data set to UI successfully")
        except Exception as e:
            logger.error(f"Error setting ability data to UI: {e}")
    
    @staticmethod
    def load_ability_for_ui(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Load ability for UI consumption (returns dictionary)
        """
        return DirectDataManager.load_ability(filename)
    
    @staticmethod
    def save_ability_from_ui(editor, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save ability from UI data directly to JSON
        """
        try:
            # Get data from UI
            ability_data = SimpleBridge.get_ability_data_from_ui(editor)
            
            # Save directly
            return DirectDataManager.save_ability(ability_data, filename)
            
        except Exception as e:
            logger.error(f"Error saving ability from UI: {e}")
            return False, str(e)
    
    @staticmethod
    def list_abilities() -> list:
        """Get list of available abilities"""
        return DirectDataManager.list_abilities()
    
    @staticmethod
    def delete_ability(filename: str) -> Tuple[bool, Optional[str]]:
        """Delete an ability file"""
        return DirectDataManager.delete_ability(filename)
    
    # ========== VALIDATION (OPTIONAL) ==========
    
    @staticmethod
    def validate_piece_data(data: Dict[str, Any]) -> Tuple[bool, list, list]:
        """
        Optional validation for piece data
        Returns: (is_valid, errors, warnings)
        """
        errors = []
        warnings = []
        
        # Basic validation only
        if not data.get('name', '').strip():
            errors.append("Piece name is required")
        
        if 'movement' not in data:
            errors.append("Movement data is required")
        elif not isinstance(data['movement'], dict):
            errors.append("Movement data must be a dictionary")
        elif 'type' not in data['movement']:
            errors.append("Movement type is required")
        
        # Check for required fields
        required_fields = ['version', 'role', 'canCapture', 'abilities', 'maxPoints', 'startingPoints', 'rechargeType']
        for field in required_fields:
            if field not in data:
                warnings.append(f"Missing field: {field}")
        
        is_valid = len(errors) == 0
        return is_valid, errors, warnings
    
    @staticmethod
    def validate_ability_data(data: Dict[str, Any]) -> Tuple[bool, list, list]:
        """
        Optional validation for ability data
        Returns: (is_valid, errors, warnings)
        """
        errors = []
        warnings = []
        
        # Basic validation only
        if not data.get('name', '').strip():
            errors.append("Ability name is required")
        
        if 'tags' not in data:
            warnings.append("Missing tags field")
        elif not isinstance(data['tags'], list):
            errors.append("Tags must be a list")
        
        # Check for required fields
        required_fields = ['version', 'description', 'cost']
        for field in required_fields:
            if field not in data:
                warnings.append(f"Missing field: {field}")
        
        is_valid = len(errors) == 0
        return is_valid, errors, warnings


# Create a global instance for easy access
simple_bridge = SimpleBridge()
