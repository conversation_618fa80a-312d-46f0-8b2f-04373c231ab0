"""
Base Manager Classes for Adventure Chess Creator

This module provides base classes for various manager components used throughout
the application, consolidating common patterns and reducing code duplication.

Base Classes:
- BaseManager: Foundation for all manager classes
- BaseUIManager: Base for UI component managers
- BaseDataManager: Base for data management classes
- BaseConfigurationManager: Base for configuration managers
"""

import logging
from typing import Dict, Any, List, Optional, Callable, Union
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class BaseManager(ABC):
    """
    Abstract base class for all manager components in Adventure Chess Creator.
    
    Provides:
    - Standardized initialization and cleanup
    - Error handling and logging
    - State management
    - Event handling patterns
    """
    
    def __init__(self, parent_instance, manager_type: str = ""):
        """
        Initialize the base manager.
        
        Args:
            parent_instance: The parent object (usually an editor)
            manager_type: Type identifier for the manager
        """
        self.parent = parent_instance
        self.manager_type = manager_type
        self.is_initialized = False
        self.error_log = []
        
        # State tracking
        self.current_state = {}
        self.previous_state = {}
        
        # Event handlers
        self.event_handlers = {}
        
        # Initialize the manager
        self.initialize()
    
    def initialize(self):
        """Initialize the manager - override in subclasses"""
        try:
            self.setup_manager()
            self.is_initialized = True
            logger.debug(f"{self.manager_type} manager initialized successfully")
        except Exception as e:
            self.log_error(f"Failed to initialize {self.manager_type} manager", e)
            raise
    
    @abstractmethod
    def setup_manager(self):
        """Setup manager-specific functionality - implement in subclasses"""
        pass
    
    def cleanup(self):
        """Cleanup manager resources"""
        try:
            self.cleanup_manager()
            self.is_initialized = False
            logger.debug(f"{self.manager_type} manager cleaned up successfully")
        except Exception as e:
            self.log_error(f"Error during {self.manager_type} manager cleanup", e)
    
    def cleanup_manager(self):
        """Cleanup manager-specific resources - override in subclasses"""
        pass
    
    def log_error(self, message: str, exception: Optional[Exception] = None):
        """Log an error with context"""
        full_message = f"{self.manager_type}: {message}"
        if exception:
            full_message += f" - {str(exception)}"
        
        logger.error(full_message)
        self.error_log.append(full_message)
    
    def get_errors(self) -> List[str]:
        """Get all logged errors"""
        return self.error_log.copy()
    
    def clear_errors(self):
        """Clear the error log"""
        self.error_log.clear()
    
    def save_state(self):
        """Save current state for rollback"""
        self.previous_state = self.current_state.copy()
    
    def restore_state(self):
        """Restore previous state"""
        if self.previous_state:
            self.current_state = self.previous_state.copy()
            self.apply_state()
    
    def apply_state(self):
        """Apply current state - override in subclasses"""
        pass
    
    def register_event_handler(self, event_name: str, handler: Callable):
        """Register an event handler"""
        if event_name not in self.event_handlers:
            self.event_handlers[event_name] = []
        self.event_handlers[event_name].append(handler)
    
    def emit_event(self, event_name: str, *args, **kwargs):
        """Emit an event to all registered handlers"""
        if event_name in self.event_handlers:
            for handler in self.event_handlers[event_name]:
                try:
                    handler(*args, **kwargs)
                except Exception as e:
                    self.log_error(f"Error in event handler for {event_name}", e)


class BaseUIManager(BaseManager):
    """
    Base class for UI component managers.
    
    Provides:
    - Widget creation and management
    - Layout management
    - Event connection patterns
    - UI state synchronization
    """
    
    def __init__(self, parent_instance, manager_type: str = "UI"):
        self.widgets = {}  # Maps widget names to widget instances
        self.layouts = {}  # Maps layout names to layout instances
        self.connections = []  # List of signal connections for cleanup
        
        super().__init__(parent_instance, manager_type)
    
    def store_widget(self, name: str, widget):
        """Store a widget for later retrieval"""
        self.widgets[name] = widget
        
        # Connect change tracking if parent supports it
        if hasattr(self.parent, 'on_data_changed'):
            self.connect_widget_change_tracking(widget)
    
    def get_widget(self, name: str):
        """Retrieve a stored widget by name"""
        return self.widgets.get(name)
    
    def store_layout(self, name: str, layout):
        """Store a layout for later retrieval"""
        self.layouts[name] = layout
    
    def get_layout(self, name: str):
        """Retrieve a stored layout by name"""
        return self.layouts.get(name)
    
    def connect_widget_change_tracking(self, widget):
        """Connect change tracking to a widget"""
        try:
            from PyQt6.QtWidgets import QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox
            
            connection = None
            if isinstance(widget, (QLineEdit, QTextEdit)):
                connection = widget.textChanged.connect(self.parent.on_data_changed)
            elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                connection = widget.valueChanged.connect(self.parent.on_data_changed)
            elif isinstance(widget, QComboBox):
                connection = widget.currentTextChanged.connect(self.parent.on_data_changed)
            elif isinstance(widget, QCheckBox):
                connection = widget.toggled.connect(self.parent.on_data_changed)
            
            if connection:
                self.connections.append(connection)
                
        except Exception as e:
            self.log_error(f"Could not connect change tracking for {type(widget).__name__}", e)
    
    def cleanup_manager(self):
        """Cleanup UI manager resources"""
        # Disconnect all signal connections
        for connection in self.connections:
            try:
                # Note: PyQt6 connections are automatically cleaned up when objects are destroyed
                pass
            except Exception as e:
                self.log_error("Error disconnecting signal", e)

        # Clear all references to prevent memory leaks
        self.connections.clear()

        # Properly cleanup widgets
        for widget in self.widgets.values():
            try:
                if hasattr(widget, 'cleanup') and callable(widget.cleanup):
                    widget.cleanup()
                elif hasattr(widget, 'deleteLater') and callable(widget.deleteLater):
                    widget.deleteLater()
            except Exception as e:
                self.log_error(f"Error cleaning up widget {type(widget).__name__}", e)

        self.widgets.clear()
        self.layouts.clear()
    
    def update_ui_state(self, state: Dict[str, Any]):
        """Update UI widgets based on state"""
        for widget_name, value in state.items():
            widget = self.get_widget(widget_name)
            if widget:
                self.set_widget_value(widget, value)
    
    def collect_ui_state(self) -> Dict[str, Any]:
        """Collect current state from UI widgets"""
        state = {}
        for widget_name, widget in self.widgets.items():
            try:
                state[widget_name] = self.get_widget_value(widget)
            except Exception as e:
                self.log_error(f"Error collecting state from widget {widget_name}", e)
        return state
    
    def set_widget_value(self, widget, value):
        """Set value for a widget based on its type"""
        try:
            from PyQt6.QtWidgets import QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox
            
            if isinstance(widget, QLineEdit):
                widget.setText(str(value) if value is not None else "")
            elif isinstance(widget, QTextEdit):
                widget.setPlainText(str(value) if value is not None else "")
            elif isinstance(widget, QSpinBox):
                widget.setValue(int(value) if value is not None else 0)
            elif isinstance(widget, QDoubleSpinBox):
                widget.setValue(float(value) if value is not None else 0.0)
            elif isinstance(widget, QComboBox):
                index = widget.findText(str(value))
                if index >= 0:
                    widget.setCurrentIndex(index)
            elif isinstance(widget, QCheckBox):
                widget.setChecked(bool(value) if value is not None else False)
        except Exception as e:
            self.log_error(f"Error setting widget value", e)
    
    def get_widget_value(self, widget):
        """Get value from a widget based on its type"""
        try:
            from PyQt6.QtWidgets import QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox
            
            if isinstance(widget, QLineEdit):
                return widget.text()
            elif isinstance(widget, QTextEdit):
                return widget.toPlainText()
            elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                return widget.value()
            elif isinstance(widget, QComboBox):
                return widget.currentText()
            elif isinstance(widget, QCheckBox):
                return widget.isChecked()
            else:
                return None
        except Exception as e:
            self.log_error(f"Error getting widget value", e)
            return None


class BaseDataManager(BaseManager):
    """
    Base class for data management components.
    
    Provides:
    - Data validation and transformation
    - Caching and state management
    - Error handling for data operations
    - Integration with data sources
    """
    
    def __init__(self, parent_instance, manager_type: str = "Data"):
        self.data_cache = {}
        self.validation_rules = {}
        self.transformation_rules = {}
        
        super().__init__(parent_instance, manager_type)
    
    def set_data(self, key: str, value: Any, validate: bool = True):
        """Set data with optional validation"""
        if validate and key in self.validation_rules:
            if not self.validate_data_item(key, value):
                raise ValueError(f"Validation failed for {key}")
        
        # Apply transformation if defined
        if key in self.transformation_rules:
            value = self.transformation_rules[key](value)
        
        self.data_cache[key] = value
        self.emit_event('data_changed', key, value)
    
    def get_data(self, key: str, default: Any = None) -> Any:
        """Get data with optional default"""
        return self.data_cache.get(key, default)
    
    def has_data(self, key: str) -> bool:
        """Check if data exists for key"""
        return key in self.data_cache
    
    def remove_data(self, key: str):
        """Remove data for key"""
        if key in self.data_cache:
            del self.data_cache[key]
            self.emit_event('data_removed', key)
    
    def clear_data(self):
        """Clear all cached data"""
        self.data_cache.clear()
        self.emit_event('data_cleared')
    
    def add_validation_rule(self, key: str, validator: Callable[[Any], bool]):
        """Add a validation rule for a data key"""
        self.validation_rules[key] = validator
    
    def add_transformation_rule(self, key: str, transformer: Callable[[Any], Any]):
        """Add a transformation rule for a data key"""
        self.transformation_rules[key] = transformer
    
    def validate_data_item(self, key: str, value: Any) -> bool:
        """Validate a single data item"""
        if key not in self.validation_rules:
            return True
        
        try:
            return self.validation_rules[key](value)
        except Exception as e:
            self.log_error(f"Validation error for {key}", e)
            return False
    
    def validate_all_data(self) -> List[str]:
        """Validate all cached data"""
        errors = []
        for key, value in self.data_cache.items():
            if not self.validate_data_item(key, value):
                errors.append(f"Validation failed for {key}")
        return errors


class BaseConfigurationManager(BaseManager):
    """
    Base class for configuration management components.
    
    Provides:
    - Configuration loading and saving
    - Default value management
    - Configuration validation
    - Change tracking and notifications
    """
    
    def __init__(self, parent_instance, manager_type: str = "Configuration"):
        self.config_data = {}
        self.default_config = {}
        self.config_schema = {}
        
        super().__init__(parent_instance, manager_type)
    
    def set_default_config(self, config: Dict[str, Any]):
        """Set default configuration values"""
        self.default_config = config.copy()
        
        # Initialize with defaults if no config exists
        if not self.config_data:
            self.config_data = config.copy()
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get configuration value with fallback to default"""
        if key in self.config_data:
            return self.config_data[key]
        elif key in self.default_config:
            return self.default_config[key]
        else:
            return default
    
    def set_config_value(self, key: str, value: Any):
        """Set configuration value"""
        old_value = self.config_data.get(key)
        self.config_data[key] = value
        
        if old_value != value:
            self.emit_event('config_changed', key, value, old_value)
    
    def reset_to_defaults(self):
        """Reset configuration to default values"""
        self.config_data = self.default_config.copy()
        self.emit_event('config_reset')
    
    def validate_config(self) -> List[str]:
        """Validate current configuration"""
        errors = []
        
        for key, schema in self.config_schema.items():
            value = self.get_config_value(key)
            
            # Check required fields
            if schema.get('required', False) and value is None:
                errors.append(f"Required configuration '{key}' is missing")
                continue
            
            # Check type
            expected_type = schema.get('type')
            if expected_type and value is not None and not isinstance(value, expected_type):
                errors.append(f"Configuration '{key}' must be of type {expected_type.__name__}")
            
            # Check custom validator
            validator = schema.get('validator')
            if validator and value is not None:
                try:
                    if not validator(value):
                        errors.append(f"Validation failed for configuration '{key}'")
                except Exception as e:
                    errors.append(f"Validation error for '{key}': {str(e)}")
        
        return errors
