#!/usr/bin/env python3
"""
Test script to check piece editor initialization and display
"""

import sys
from PyQt6.QtWidgets import QApplication
from editors.piece_editor import PieceEditorWindow

def test_piece_editor():
    app = QApplication(sys.argv)

    try:
        print("Creating piece editor...")

        # Enable debug logging
        import logging
        logging.basicConfig(level=logging.DEBUG)

        editor = PieceEditorWindow()
        print("Piece editor created successfully")

        print(f"Central widget: {editor.centralWidget()}")
        print(f"Has UI components: {hasattr(editor, 'ui_components')}")
        print(f"Has data handler: {hasattr(editor, 'data_handler')}")
        print(f"Has movement manager: {hasattr(editor, 'movement_manager')}")

        if hasattr(editor, 'ui_components'):
            print(f"UI components type: {type(editor.ui_components)}")

        # Check if widgets exist
        if hasattr(editor, 'name_edit'):
            print(f"Name edit widget: {editor.name_edit}")
        else:
            print("No name_edit widget found")

        if hasattr(editor, 'description_edit'):
            print(f"Description edit widget: {editor.description_edit}")
        else:
            print("No description_edit widget found")

        print("Showing piece editor...")
        editor.show()
        print("Piece editor shown")

        # Don't run the event loop, just test creation
        return True

    except Exception as e:
        print(f"Error creating piece editor: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_piece_editor()
    print(f"Test {'PASSED' if success else 'FAILED'}")
