"""
Enhanced Grid Visualization for Adventure Chess Creator

This module provides enhanced grid visualization with improved colors, patterns,
and interactive features for pattern editing.
"""

import logging
from typing import List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFrame, 
    QLabel, QPushButton
)
from PyQt6.QtCore import Qt

from .color_schemes import ColorSchemes

logger = logging.getLogger(__name__)


class EnhancedGridVisualization(QWidget):
    """Enhanced grid visualization with improved colors and patterns"""
    
    def __init__(self, rows: int = 8, cols: int = 8, parent=None):
        super().__init__(parent)
        self.rows = rows
        self.cols = cols
        self.grid_data = [[0 for _ in range(cols)] for _ in range(rows)]
        self.cell_size = 35
        self.setup_ui()
    
    def setup_ui(self):
        """Setup enhanced grid UI"""
        layout = QVBoxLayout()
        
        # Legend
        legend_frame = QFrame()
        legend_layout = QHBoxLayout()
        
        legend_items = [
            ("Empty", ColorSchemes.GRID_EMPTY),
            ("Move", ColorSchemes.GRID_MOVE),
            ("Attack", ColorSchemes.GRID_ATTACK),
            ("Both", ColorSchemes.GRID_BOTH),
            ("Action", ColorSchemes.GRID_ACTION),
            ("Any", ColorSchemes.GRID_ANY),
            ("Target", ColorSchemes.GRID_TARGET)
        ]
        
        for label, color in legend_items:
            legend_item = self._create_legend_item(label, color)
            legend_layout.addWidget(legend_item)
        
        legend_layout.addStretch()
        legend_frame.setLayout(legend_layout)
        
        # Grid
        self.grid_widget = QWidget()
        self.grid_layout = QGridLayout()
        self.grid_layout.setSpacing(1)
        self.buttons = []
        
        for row in range(self.rows):
            button_row = []
            for col in range(self.cols):
                btn = QPushButton()
                btn.setFixedSize(self.cell_size, self.cell_size)
                btn.setCheckable(True)
                btn.clicked.connect(lambda checked, r=row, c=col: self.cell_clicked(r, c))
                self._update_cell_style(btn, 0)
                self.grid_layout.addWidget(btn, row, col)
                button_row.append(btn)
            self.buttons.append(button_row)
        
        self.grid_widget.setLayout(self.grid_layout)
        
        layout.addWidget(legend_frame)
        layout.addWidget(self.grid_widget)
        self.setLayout(layout)
    
    def _create_legend_item(self, label: str, color: str) -> QWidget:
        """Create a legend item"""
        item = QFrame()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 2, 5, 2)
        
        color_box = QLabel()
        color_box.setFixedSize(16, 16)
        color_box.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border: 1px solid {ColorSchemes.GRID_BORDER};
                border-radius: 2px;
            }}
        """)
        
        text_label = QLabel(label)
        text_label.setStyleSheet("font-size: 11px; color: #495057;")
        
        layout.addWidget(color_box)
        layout.addWidget(text_label)
        item.setLayout(layout)
        
        return item
    
    def _update_cell_style(self, button: QPushButton, value: int):
        """Update cell style based on value"""
        colors = {
            0: ColorSchemes.GRID_EMPTY,
            1: ColorSchemes.GRID_MOVE,
            2: ColorSchemes.GRID_ATTACK,
            3: ColorSchemes.GRID_BOTH,
            4: ColorSchemes.GRID_ACTION,
            5: ColorSchemes.GRID_ANY,
            6: ColorSchemes.GRID_TARGET
        }
        
        color = colors.get(value, ColorSchemes.GRID_EMPTY)
        text_color = "#ffffff" if value > 0 else "#666666"
        
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                border: 1px solid {ColorSchemes.GRID_BORDER};
                border-radius: 3px;
                color: {text_color};
                font-weight: bold;
                font-size: 10px;
            }}
            QPushButton:hover {{
                border: 2px solid #ffffff;
            }}
            QPushButton:pressed {{
                background-color: {ColorSchemes.HOVER_BG};
            }}
        """)
        
        # Add symbols for different types
        symbols = {
            0: "",
            1: "→",
            2: "⚔",
            3: "⚡",
            4: "✦",
            5: "◆",
            6: "🎯"
        }
        button.setText(symbols.get(value, ""))
    
    def cell_clicked(self, row: int, col: int):
        """Handle cell click"""
        # Cycle through values 0-6
        current_value = self.grid_data[row][col]
        new_value = (current_value + 1) % 7
        self.grid_data[row][col] = new_value
        self._update_cell_style(self.buttons[row][col], new_value)
    
    def set_grid_data(self, data: List[List[int]]):
        """Set grid data and update display"""
        self.grid_data = data
        for row in range(self.rows):
            for col in range(self.cols):
                if row < len(data) and col < len(data[row]):
                    value = data[row][col]
                    self.grid_data[row][col] = value
                    self._update_cell_style(self.buttons[row][col], value)
    
    def get_grid_data(self) -> List[List[int]]:
        """Get current grid data"""
        return self.grid_data


class GridPatternEditor(QWidget):
    """Enhanced pattern editor with grid visualization"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup pattern editor UI"""
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("Pattern Editor")
        title.setStyleSheet("font-size: 14px; font-weight: bold; padding: 5px;")
        
        # Grid visualization
        self.grid = EnhancedGridVisualization(8, 8)
        
        # Controls
        controls_frame = QFrame()
        controls_layout = QHBoxLayout()
        
        clear_btn = QPushButton("Clear All")
        clear_btn.clicked.connect(self.clear_grid)
        clear_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ColorSchemes.WARNING};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #e0a800;
            }}
        """)
        
        reset_btn = QPushButton("Reset")
        reset_btn.clicked.connect(self.reset_grid)
        reset_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ColorSchemes.INFO};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #138496;
            }}
        """)
        
        controls_layout.addWidget(clear_btn)
        controls_layout.addWidget(reset_btn)
        controls_layout.addStretch()
        controls_frame.setLayout(controls_layout)
        
        layout.addWidget(title)
        layout.addWidget(self.grid)
        layout.addWidget(controls_frame)
        self.setLayout(layout)
    
    def clear_grid(self):
        """Clear all grid cells"""
        empty_data = [[0 for _ in range(8)] for _ in range(8)]
        self.grid.set_grid_data(empty_data)
    
    def reset_grid(self):
        """Reset grid to default pattern"""
        # Default pattern - center cross
        default_data = [[0 for _ in range(8)] for _ in range(8)]
        default_data[3][4] = 1  # Move right
        default_data[4][3] = 1  # Move left
        default_data[4][5] = 1  # Move right
        default_data[5][4] = 1  # Move down
        self.grid.set_grid_data(default_data)
    
    def get_pattern_data(self) -> List[List[int]]:
        """Get current pattern data"""
        return self.grid.get_grid_data()
    
    def set_pattern_data(self, data: List[List[int]]):
        """Set pattern data"""
        self.grid.set_grid_data(data)
