"""
Specialized Data Handlers for Adventure Chess Creator

This module provides specialized data handlers that inherit from BaseDataHandler
and implement type-specific functionality for pieces and abilities.

Classes:
- EnhancedPieceDataHandler: Specialized handler for piece data
- EnhancedAbilityDataHandler: Specialized handler for ability data
"""

import logging
from typing import Dict, Any, List, Optional

from core.base_classes.base_data_handler import BaseDataHandler
from config import DEFAULT_ABILITY

logger = logging.getLogger(__name__)


class EnhancedPieceDataHandler(BaseDataHandler):
    """
    Enhanced piece data handler that extends BaseDataHandler with
    piece-specific functionality.
    """
    
    def __init__(self, editor_instance):
        super().__init__(editor_instance, "piece")
    
    def collect_type_specific_data(self) -> Dict[str, Any]:
        """Collect piece-specific data"""
        data = {}

        # Handle movement data - use consolidated movement data as single source of truth
        if hasattr(self.editor, 'current_movement_data') and self.editor.current_movement_data:
            data['movement'] = self.editor.current_movement_data.copy()

        # Handle icon data from icon manager
        if hasattr(self.editor, 'icon_manager') and self.editor.icon_manager:
            try:
                icon_data = self.editor.icon_manager.get_icon_data()
                data.update(icon_data)
            except Exception as e:
                logger.warning(f"Error collecting icon data: {e}")

        # Handle promotion data from promotion manager
        if hasattr(self.editor, 'promotion_manager') and self.editor.promotion_manager:
            try:
                promotion_data = self.editor.promotion_manager.get_promotion_data()
                data.update(promotion_data)
            except Exception as e:
                logger.warning(f"Error collecting promotion data: {e}")

        # Handle abilities list
        if hasattr(self.editor, 'abilities'):
            data['abilities'] = self.editor.abilities.copy()

        return data
    
    def populate_type_specific_data(self, data: Dict[str, Any]):
        """Populate piece-specific data"""
        # Load movement pattern data specifically
        self.load_movement_pattern_data(data)

        # Load icon data and update previews
        self.load_icon_data(data)
    
    def validate_type_specific_data(self, data: Dict[str, Any]) -> List[str]:
        """Validate piece-specific data"""
        errors = []
        
        # Validate movement data
        if 'movement' not in data:
            errors.append("Movement data is required")
        elif not isinstance(data['movement'], dict):
            errors.append("Movement data must be a dictionary")
        elif 'type' not in data['movement']:
            errors.append("Movement type is required")
        
        # Validate role
        valid_roles = ['Commander', 'Guardian', 'Assassin', 'Support', 'Specialist']
        if data.get('role') not in valid_roles:
            errors.append(f"Role must be one of: {', '.join(valid_roles)}")
        
        # Validate points
        max_points = data.get('maxPoints', 0)
        starting_points = data.get('startingPoints', 0)
        
        if max_points < 0:
            errors.append("Max points cannot be negative")
        if starting_points < 0:
            errors.append("Starting points cannot be negative")
        if starting_points > max_points:
            errors.append("Starting points cannot exceed max points")
        
        return errors
    
    def ensure_required_fields(self, data: Dict[str, Any]):
        """Ensure piece-specific required fields exist"""
        data.setdefault('role', 'Commander')
        data.setdefault('canCapture', True)
        data.setdefault('canCastle', False)
        data.setdefault('colorDirectional', False)
        data.setdefault('abilities', [])
        data.setdefault('maxPoints', 1)
        data.setdefault('startingPoints', 1)
        data.setdefault('rechargeType', 'turnRecharge')
        
        # Ensure movement data exists
        if 'movement' not in data:
            data['movement'] = {
                'type': 'orthogonal',
                'pattern': None,
                'piecePosition': [3, 3]
            }
    
    def get_default_data(self) -> Dict[str, Any]:
        """Get default piece data structure"""
        return {
            "version": "1.0.0",
            "name": "",
            "description": "",
            "role": "Commander",
            "canCastle": False,
            "movement": {"type": "orthogonal", "pattern": None, "piecePosition": [3, 3]},
            "colorDirectional": False,
            "canCapture": True,
            "abilities": [],
            "maxPoints": 1,
            "startingPoints": 1,
            "rechargeType": "turnRecharge"
        }
    
    def reset_to_defaults(self):
        """Reset piece-specific data to defaults"""
        # Reset pattern data
        if hasattr(self.editor, 'current_custom_pattern'):
            self.editor.current_custom_pattern = [[0 for _ in range(8)] for _ in range(8)]
        if hasattr(self.editor, 'custom_pattern_piece_pos'):
            self.editor.custom_pattern_piece_pos = [3, 3]
        if hasattr(self.editor, 'selected_movement_type'):
            self.editor.selected_movement_type = "orthogonal"
        if hasattr(self.editor, 'current_movement_data'):
            self.editor.current_movement_data = {
                "type": "orthogonal", 
                "pattern": None, 
                "piecePosition": [3, 3]
            }
    
    def post_populate_update(self):
        """Update piece-specific UI components after data population"""
        try:
            if hasattr(self.editor, 'update_movement_controls'):
                self.editor.update_movement_controls()
            if hasattr(self.editor, 'update_recharge_options'):
                self.editor.update_recharge_options()
            if hasattr(self.editor, 'update_icon_previews'):
                self.editor.update_icon_previews()
            if hasattr(self.editor, 'update_movement_pattern_preview'):
                self.editor.update_movement_pattern_preview()
            if hasattr(self.editor, 'refresh_abilities_list'):
                self.editor.refresh_abilities_list()
            if hasattr(self.editor, 'on_role_changed'):
                self.editor.on_role_changed()
        except Exception as e:
            logger.warning(f"Error updating piece UI components: {e}")
    
    def load_movement_pattern_data(self, piece_data: Dict[str, Any]):
        """Load movement pattern data from piece data into consolidated storage"""
        if 'movement' not in piece_data:
            return

        movement = piece_data['movement']

        # Store complete movement data as single source of truth
        if hasattr(self.editor, 'current_movement_data'):
            self.editor.current_movement_data = movement.copy()

    def load_icon_data(self, piece_data: Dict[str, Any]) -> None:
        """
        Load icon data from piece data and update icon manager.

        Args:
            piece_data: Dictionary containing piece data
        """
        try:
            # Check if editor has icon manager
            if not hasattr(self.editor, 'icon_manager') or not self.editor.icon_manager:
                return

            # Extract icon data from piece data (try both snake_case and camelCase)
            black_icon = piece_data.get('black_icon') or piece_data.get('blackIcon')
            white_icon = piece_data.get('white_icon') or piece_data.get('whiteIcon')

            # Convert to format expected by icon manager (snake_case)
            icon_data = {
                'black_icon': black_icon,
                'white_icon': white_icon
            }

            # Set icon data using icon manager
            self.editor.icon_manager.set_icon_data(icon_data)

            logger.info(f"Loaded icon data: black={black_icon}, white={white_icon}")

        except Exception as e:
            logger.error(f"Error loading icon data: {e}")


class EnhancedAbilityDataHandler(BaseDataHandler):
    """
    Enhanced ability data handler that extends BaseDataHandler with
    ability-specific functionality.
    """
    
    def __init__(self, editor_instance):
        super().__init__(editor_instance, "ability")
    
    def collect_type_specific_data(self) -> Dict[str, Any]:
        """Collect ability-specific data"""
        data = {}
        
        # Collect tag-specific data from the tag manager
        if hasattr(self.editor, 'tag_manager'):
            try:
                tag_data = self.editor.tag_manager.collect_tag_data()
                data.update(tag_data)
            except Exception as e:
                logger.warning(f"Error collecting tag data: {e}")
        
        return data
    
    def populate_type_specific_data(self, data: Dict[str, Any]):
        """Populate ability-specific data"""
        # Populate tag-specific data through the tag manager
        if hasattr(self.editor, 'tag_manager'):
            try:
                self.editor.tag_manager.populate_tag_data(data)
            except Exception as e:
                logger.warning(f"Error populating tag data: {e}")
    
    def validate_type_specific_data(self, data: Dict[str, Any]) -> List[str]:
        """Validate ability-specific data"""
        errors = []
        
        # Validate cost
        cost = data.get('cost', 0)
        if not isinstance(cost, (int, float)) or cost < 0:
            errors.append("Cost must be a non-negative number")
        
        # Validate activation mode
        valid_modes = ['auto', 'manual', 'passive', 'triggered']
        if data.get('activationMode') not in valid_modes:
            errors.append(f"Activation mode must be one of: {', '.join(valid_modes)}")
        
        # Validate tags
        tags = data.get('tags', [])
        if not isinstance(tags, list):
            errors.append("Tags must be a list")
        
        # Validate tag-specific data through tag manager
        if hasattr(self.editor, 'tag_manager'):
            try:
                tag_errors = self.editor.tag_manager.validate_tag_data(data)
                errors.extend(tag_errors)
            except Exception as e:
                logger.warning(f"Error validating tag data: {e}")
                errors.append(f"Tag validation error: {str(e)}")
        
        return errors
    
    def ensure_required_fields(self, data: Dict[str, Any]):
        """Ensure ability-specific required fields exist"""
        data.setdefault('cost', 0)
        data.setdefault('activationMode', 'auto')
        data.setdefault('tags', [])
        
        # Use default ability configuration if available
        if hasattr(DEFAULT_ABILITY, '__dict__'):
            for key, value in DEFAULT_ABILITY.__dict__.items():
                if not key.startswith('_'):
                    data.setdefault(key, value)
    
    def get_default_data(self) -> Dict[str, Any]:
        """Get default ability data structure"""
        default_data = {
            'version': '1.0.0',
            'name': '',
            'description': '',
            'cost': 0,
            'activationMode': 'auto',
            'tags': []
        }
        
        # Add default ability configuration if available
        if hasattr(DEFAULT_ABILITY, '__dict__'):
            for key, value in DEFAULT_ABILITY.__dict__.items():
                if not key.startswith('_'):
                    default_data.setdefault(key, value)
        
        return default_data
    
    def post_populate_update(self):
        """Update ability-specific UI components after data population"""
        try:
            if hasattr(self.editor, 'update_tag_configuration'):
                self.editor.update_tag_configuration()
            if hasattr(self.editor, 'refresh_tag_list'):
                self.editor.refresh_tag_list()
            if hasattr(self.editor, 'update_preview'):
                self.editor.update_preview()
        except Exception as e:
            logger.warning(f"Error updating ability UI components: {e}")
