"""
Core module for Adventure Chess Creator

Location: core/

This module contains the foundational classes and interfaces that form
the backbone of the Adventure Chess Creator application.

Directory structure:
/base_classes/     - Abstract base classes for widgets, managers, handlers, and editors
/handlers/         - Specialized data handlers for pieces and abilities
/managers/         - Manager classes for various application components
/interfaces/       - Interface definitions for data handling and UI interaction
/ui/              - Core UI components and utilities
/error_handling/  - Error handling and user-friendly error systems
/performance/     - Performance optimization and caching systems
/security/        - Security validation and data protection
/validation/      - Data validation rules and real-time validation
/workflow/        - Template and workflow management systems

Used by: All editors and application components for foundational functionality
"""

# Import key base classes for easy access
from .base_classes.base_widgets import BaseWidget, BaseFormWidget, BaseTabWidget, BaseDataWidget
from .base_classes.base_manager import BaseManager, BaseUIManager, BaseDataManager, BaseConfigurationManager
from .base_classes.base_data_handler import BaseDataHandler
from .base_classes.base_editor import BaseEditor

# Import specialized implementations
from .handlers.specialized_data_handlers import <PERSON>hanced<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EnhancedAbilityDataHandler
from .interfaces.editor_data_interface import EditorDataInterface

# Import error handling system
from .error_handling import (
    ErrorSeverity, ErrorCategory, ErrorContext, EnhancedErrorHandler,
    EnhancedDirectDataManager, UserFriendlyError, ErrorMessageTranslator,
    UserFriendlyErrorDialog, safe_file_load, safe_file_save,
    show_user_friendly_error, error_handler
)

# Import performance system
from .performance import (
    get_file_system_optimizer, SearchResult, FileSystemOptimizer,
    get_lazy_manager, LazyDataManager, get_cache_manager
)

# Import security system
from .security import (
    SecurityValidator, security_validator, SecureDataManager,
    secure_data_manager, get_recovery_options
)

# Import validation system
from .validation import (
    ValidationRules, EnhancedValidationMixin,
    create_piece_validation_rules, create_ability_validation_rules,
    RealTimeValidationWidget
)

__all__ = [
    # Base classes
    'BaseWidget', 'BaseFormWidget', 'BaseTabWidget', 'BaseDataWidget',
    'BaseManager', 'BaseUIManager', 'BaseDataManager', 'BaseConfigurationManager',
    'BaseDataHandler', 'BaseEditor',

    # Specialized implementations
    'EnhancedPieceDataHandler', 'EnhancedAbilityDataHandler',
    'EditorDataInterface',

    # Error handling system
    'ErrorSeverity', 'ErrorCategory', 'ErrorContext', 'EnhancedErrorHandler',
    'EnhancedDirectDataManager', 'UserFriendlyError', 'ErrorMessageTranslator',
    'UserFriendlyErrorDialog', 'safe_file_load', 'safe_file_save',
    'show_user_friendly_error', 'error_handler',

    # Performance system
    'get_file_system_optimizer', 'SearchResult', 'FileSystemOptimizer',
    'get_lazy_manager', 'LazyDataManager', 'get_cache_manager',

    # Security system
    'SecurityValidator', 'security_validator', 'SecureDataManager',
    'secure_data_manager', 'get_recovery_options',

    # Validation system
    'ValidationRules', 'EnhancedValidationMixin',
    'create_piece_validation_rules', 'create_ability_validation_rules',
    'RealTimeValidationWidget'
]
