"""
Template System for Adventure Chess Creator

This module provides template management for common pieces and abilities
with default templates and custom template creation capabilities.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, List
from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QPushButton, QLabel, QTextEdit

logger = logging.getLogger(__name__)


class TemplateManager:
    """Manages templates for common pieces and abilities"""

    def __init__(self, editor):
        self.editor = editor
        self.templates_dir = Path("data") / "templates"
        self.templates_dir.mkdir(exist_ok=True)
        self.piece_templates = {}
        self.ability_templates = {}
        self.load_templates()

    def load_templates(self):
        """Load all available templates"""
        try:
            # Load piece templates
            piece_templates_file = self.templates_dir / "piece_templates.json"
            if piece_templates_file.exists():
                with open(piece_templates_file, 'r', encoding='utf-8') as f:
                    self.piece_templates = json.load(f)
            else:
                self.piece_templates = self.create_default_piece_templates()
                self.save_piece_templates()

            # Load ability templates
            ability_templates_file = self.templates_dir / "ability_templates.json"
            if ability_templates_file.exists():
                with open(ability_templates_file, 'r', encoding='utf-8') as f:
                    self.ability_templates = json.load(f)
            else:
                self.ability_templates = self.create_default_ability_templates()
                self.save_ability_templates()

            logger.info(f"Loaded {len(self.piece_templates)} piece templates and {len(self.ability_templates)} ability templates")

        except Exception as e:
            logger.error(f"Error loading templates: {e}")

    def create_default_piece_templates(self) -> Dict[str, Dict[str, Any]]:
        """Create default piece templates"""
        return {
            "Basic Pawn": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic pawn piece",
                "role": "Soldier",
                "can_castle": False,
                "track_starting_position": True,
                "color_directional": True,
                "can_capture": True,
                "movement": {
                    "type": "Custom",
                    "pattern": self._create_pawn_pattern(),
                    "piece_position": [4, 4]
                },
                "recharge": {"type": "None", "turns": 0},
                "abilities": [],
                "promotions": []
            },
            "Basic Rook": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic rook piece",
                "role": "Commander",
                "can_castle": True,
                "track_starting_position": False,
                "color_directional": False,
                "can_capture": True,
                "movement": {
                    "type": "orthogonal",
                    "distance": 8
                },
                "recharge": {"type": "None", "turns": 0},
                "abilities": [],
                "promotions": []
            },
            "Basic Knight": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic knight piece",
                "role": "Commander",
                "can_castle": False,
                "track_starting_position": False,
                "color_directional": False,
                "can_capture": True,
                "movement": {
                    "type": "L-shape",
                    "distance": 1
                },
                "recharge": {"type": "None", "turns": 0},
                "abilities": [],
                "promotions": []
            },
            "Basic Bishop": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic bishop piece",
                "role": "Commander",
                "can_castle": False,
                "track_starting_position": False,
                "color_directional": False,
                "can_capture": True,
                "movement": {
                    "type": "diagonal",
                    "distance": 8
                },
                "recharge": {"type": "None", "turns": 0},
                "abilities": [],
                "promotions": []
            },
            "Basic Queen": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic queen piece",
                "role": "Commander",
                "can_castle": False,
                "track_starting_position": False,
                "color_directional": False,
                "can_capture": True,
                "movement": {
                    "type": "orthogonal_diagonal",
                    "distance": 8
                },
                "recharge": {"type": "None", "turns": 0},
                "abilities": [],
                "promotions": []
            },
            "Basic King": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic king piece",
                "role": "King",
                "can_castle": True,
                "track_starting_position": True,
                "color_directional": False,
                "can_capture": True,
                "movement": {
                    "type": "orthogonal_diagonal",
                    "distance": 1
                },
                "recharge": {"type": "None", "turns": 0},
                "abilities": [],
                "promotions": []
            }
        }

    def create_default_ability_templates(self) -> Dict[str, Dict[str, Any]]:
        """Create default ability templates"""
        return {
            "Basic Attack": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic attack ability",
                "cost": 1,
                "tags": ["damage"],
                "recharge": {"type": "None", "turns": 0}
            },
            "Healing": {
                "version": "1.0.0",
                "name": "",
                "description": "A healing ability",
                "cost": 2,
                "tags": ["heal"],
                "recharge": {"type": "Turns", "turns": 3}
            },
            "Shield": {
                "version": "1.0.0",
                "name": "",
                "description": "A defensive shield ability",
                "cost": 1,
                "tags": ["defense"],
                "recharge": {"type": "Turns", "turns": 2}
            },
            "Teleport": {
                "version": "1.0.0",
                "name": "",
                "description": "A teleportation ability",
                "cost": 3,
                "tags": ["movement"],
                "recharge": {"type": "Turns", "turns": 5}
            }
        }

    def _create_pawn_pattern(self) -> List[List[bool]]:
        """Create a basic pawn movement pattern"""
        pattern = [[False] * 9 for _ in range(9)]
        # Forward movement
        pattern[3][4] = True  # One square forward
        pattern[2][4] = True  # Two squares forward (for starting position)
        # Diagonal capture
        pattern[3][3] = True  # Diagonal left
        pattern[3][5] = True  # Diagonal right
        return pattern

    def save_piece_templates(self):
        """Save piece templates to file"""
        try:
            piece_templates_file = self.templates_dir / "piece_templates.json"
            with open(piece_templates_file, 'w', encoding='utf-8') as f:
                json.dump(self.piece_templates, f, indent=2)
            logger.info("Piece templates saved")
        except Exception as e:
            logger.error(f"Error saving piece templates: {e}")

    def save_ability_templates(self):
        """Save ability templates to file"""
        try:
            ability_templates_file = self.templates_dir / "ability_templates.json"
            with open(ability_templates_file, 'w', encoding='utf-8') as f:
                json.dump(self.ability_templates, f, indent=2)
            logger.info("Ability templates saved")
        except Exception as e:
            logger.error(f"Error saving ability templates: {e}")

    def add_piece_template(self, name: str, data: Dict[str, Any]):
        """Add a new piece template"""
        self.piece_templates[name] = data
        self.save_piece_templates()
        logger.info(f"Added piece template: {name}")

    def add_ability_template(self, name: str, data: Dict[str, Any]):
        """Add a new ability template"""
        self.ability_templates[name] = data
        self.save_ability_templates()
        logger.info(f"Added ability template: {name}")

    def remove_piece_template(self, name: str):
        """Remove a piece template"""
        if name in self.piece_templates:
            del self.piece_templates[name]
            self.save_piece_templates()
            logger.info(f"Removed piece template: {name}")

    def remove_ability_template(self, name: str):
        """Remove an ability template"""
        if name in self.ability_templates:
            del self.ability_templates[name]
            self.save_ability_templates()
            logger.info(f"Removed ability template: {name}")

    def get_piece_template(self, name: str) -> Dict[str, Any]:
        """Get a piece template by name"""
        return self.piece_templates.get(name, {})

    def get_ability_template(self, name: str) -> Dict[str, Any]:
        """Get an ability template by name"""
        return self.ability_templates.get(name, {})

    def list_piece_templates(self) -> List[str]:
        """Get list of available piece template names"""
        return list(self.piece_templates.keys())

    def list_ability_templates(self) -> List[str]:
        """Get list of available ability template names"""
        return list(self.ability_templates.keys())

    def apply_piece_template(self, name: str):
        """Apply a piece template to the current editor"""
        template = self.get_piece_template(name)
        if template and hasattr(self.editor, 'load_data_from_dict'):
            self.editor.load_data_from_dict(template)
            logger.info(f"Applied piece template: {name}")

    def apply_ability_template(self, name: str):
        """Apply an ability template to the current editor"""
        template = self.get_ability_template(name)
        if template and hasattr(self.editor, 'load_data_from_dict'):
            self.editor.load_data_from_dict(template)
            logger.info(f"Applied ability template: {name}")

    def show_template_dialog(self):
        """Show template selection dialog"""
        dialog = TemplateDialog(self, self.editor)
        dialog.exec()


class TemplateDialog(QDialog):
    """Dialog for selecting and applying templates"""

    def __init__(self, template_manager: TemplateManager, editor):
        super().__init__(editor)
        self.template_manager = template_manager
        self.editor = editor
        self.setup_ui()

    def setup_ui(self):
        """Setup the dialog UI"""
        self.setWindowTitle("Templates")
        self.setModal(True)
        self.resize(600, 400)

        layout = QVBoxLayout(self)

        # Template list
        self.template_list = QListWidget()
        self.load_templates()
        layout.addWidget(QLabel("Available Templates:"))
        layout.addWidget(self.template_list)

        # Preview area
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        layout.addWidget(QLabel("Template Preview:"))
        layout.addWidget(self.preview_text)

        # Buttons
        button_layout = QHBoxLayout()
        self.apply_button = QPushButton("Apply Template")
        self.close_button = QPushButton("Close")

        button_layout.addWidget(self.apply_button)
        button_layout.addWidget(self.close_button)
        layout.addLayout(button_layout)

        # Connect signals
        self.template_list.currentItemChanged.connect(self.on_template_selected)
        self.apply_button.clicked.connect(self.apply_template)
        self.close_button.clicked.connect(self.close)

    def load_templates(self):
        """Load templates into the list"""
        # Determine editor type and load appropriate templates
        if hasattr(self.editor, 'data_type'):
            if self.editor.data_type == 'piece':
                templates = self.template_manager.list_piece_templates()
            elif self.editor.data_type == 'ability':
                templates = self.template_manager.list_ability_templates()
            else:
                templates = []
        else:
            # Load both types
            templates = (self.template_manager.list_piece_templates() + 
                        self.template_manager.list_ability_templates())

        for template_name in templates:
            self.template_list.addItem(template_name)

    def on_template_selected(self, current, previous):
        """Handle template selection"""
        if current:
            template_name = current.text()
            # Show preview
            if hasattr(self.editor, 'data_type') and self.editor.data_type == 'piece':
                template_data = self.template_manager.get_piece_template(template_name)
            else:
                template_data = self.template_manager.get_ability_template(template_name)

            preview_text = json.dumps(template_data, indent=2)
            self.preview_text.setPlainText(preview_text)

    def apply_template(self):
        """Apply the selected template"""
        current_item = self.template_list.currentItem()
        if current_item:
            template_name = current_item.text()
            if hasattr(self.editor, 'data_type') and self.editor.data_type == 'piece':
                self.template_manager.apply_piece_template(template_name)
            else:
                self.template_manager.apply_ability_template(template_name)
            self.close()
