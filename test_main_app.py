#!/usr/bin/env python3
"""
Test script to verify main application startup and lazy loading
"""

import sys
from PyQt6.QtWidgets import QApplication
from main import MainWindow

def test_main_app():
    app = QApplication(sys.argv)
    
    try:
        print("Creating main window...")
        main_window = MainWindow()
        print("Main window created successfully")
        
        # Test that editors are not created initially
        print(f"Piece editor initially: {main_window.editor}")
        print(f"Ability editor initially: {main_window.ability_editor}")
        
        # Test lazy loading of piece editor
        print("Testing piece editor lazy loading...")
        main_window._ensure_piece_editor()
        print(f"Piece editor after lazy load: {main_window.editor is not None}")
        
        # Test lazy loading of ability editor
        print("Testing ability editor lazy loading...")
        main_window._ensure_ability_editor()
        print(f"Ability editor after lazy load: {main_window.ability_editor is not None}")
        
        print("Showing main window...")
        main_window.show()
        print("Main window shown successfully")
        
        # Don't run the event loop, just test creation
        return True
        
    except Exception as e:
        print(f"Error testing main app: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_main_app()
    print(f"Test {'PASSED' if success else 'FAILED'}")
