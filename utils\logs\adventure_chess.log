2025-06-27 11:12:15,191 - core.workflow.workflow_integration - INFO - Auto-save system integrated
2025-06-27 11:12:15,202 - core.workflow.template_system - INFO - Loaded 8 piece templates and 7 ability templates
2025-06-27 11:12:15,203 - core.workflow.workflow_integration - INFO - Template system integrated
2025-06-27 11:12:15,205 - core.workflow.workflow_integration - INFO - Workflow integration complete for AbilityEditorWindow
2025-06-27 11:12:15,206 - core.workflow.workflow_integration - INFO - Workflow optimization integrated into AbilityEditorWindow
2025-06-27 11:12:15,207 - core.workflow.workflow_integration - INFO - Workflow menu added
2025-06-27 11:12:15,212 - core.ui.visual_feedback_integration - INFO - Visual feedback enhancements applied to AbilityEditorWindow
2025-06-27 11:12:15,214 - core.ui.visual_feedback_integration - INFO - Ability editor enhanced with visual feedback
2025-06-27 11:12:15,214 - __main__ - INFO - Ability editor created and integrated successfully
2025-06-27 11:12:58,217 - utils.direct_data_manager - INFO - Ability loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\abilities\Kings Swap.json
2025-06-27 11:12:58,219 - core.base_classes.base_data_handler - INFO - Populating ability data with 12 fields...
2025-06-27 11:12:58,223 - core.base_classes.base_data_handler - INFO - Ability data populated successfully
2025-06-27 11:12:58,224 - editors.ability_editor.ability_data_handlers - INFO - Successfully loaded ability: Kings Swap
2025-06-27 11:12:58,248 - utils.direct_data_manager - INFO - Ability loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\abilities\castle_left.json
2025-06-27 11:12:58,250 - core.base_classes.base_data_handler - INFO - Populating ability data with 9 fields...
2025-06-27 11:12:58,252 - core.base_classes.base_data_handler - INFO - Ability data populated successfully
2025-06-27 11:12:58,254 - editors.ability_editor.ability_data_handlers - INFO - Successfully loaded ability: castle_left
2025-06-27 11:12:58,287 - utils.direct_data_manager - INFO - Ability loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\abilities\castle_right.json
2025-06-27 11:12:58,288 - core.base_classes.base_data_handler - INFO - Populating ability data with 9 fields...
2025-06-27 11:12:58,290 - core.base_classes.base_data_handler - INFO - Ability data populated successfully
2025-06-27 11:12:58,291 - editors.ability_editor.ability_data_handlers - INFO - Successfully loaded ability: castle_right
2025-06-27 11:12:58,355 - utils.direct_data_manager - INFO - Ability loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\abilities\en_passant.json
2025-06-27 11:12:58,356 - core.base_classes.base_data_handler - INFO - Populating ability data with 13 fields...
2025-06-27 11:12:58,357 - core.base_classes.base_data_handler - INFO - Ability data populated successfully
2025-06-27 11:12:58,358 - editors.ability_editor.ability_data_handlers - INFO - Successfully loaded ability: en_passant
2025-06-27 11:12:58,459 - utils.direct_data_manager - INFO - Ability loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\abilities\pawn_movement_2.json
2025-06-27 11:12:58,460 - core.base_classes.base_data_handler - INFO - Populating ability data with 8 fields...
2025-06-27 11:12:58,462 - core.base_classes.base_data_handler - INFO - Ability data populated successfully
2025-06-27 11:12:58,463 - editors.ability_editor.ability_data_handlers - INFO - Successfully loaded ability: pawn_movement_2
2025-06-27 11:12:59,001 - utils.direct_data_manager - INFO - Ability loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\abilities\en_passant.json
2025-06-27 11:12:59,003 - core.base_classes.base_data_handler - INFO - Populating ability data with 13 fields...
2025-06-27 11:12:59,005 - core.base_classes.base_data_handler - INFO - Ability data populated successfully
2025-06-27 11:12:59,006 - editors.ability_editor.ability_data_handlers - INFO - Successfully loaded ability: en_passant
2025-06-27 11:12:59,032 - utils.direct_data_manager - INFO - Ability loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\abilities\castle_right.json
2025-06-27 11:12:59,033 - core.base_classes.base_data_handler - INFO - Populating ability data with 9 fields...
2025-06-27 11:12:59,035 - core.base_classes.base_data_handler - INFO - Ability data populated successfully
2025-06-27 11:12:59,036 - editors.ability_editor.ability_data_handlers - INFO - Successfully loaded ability: castle_right
2025-06-27 11:12:59,044 - utils.direct_data_manager - INFO - Ability loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\abilities\castle_left.json
2025-06-27 11:12:59,044 - core.base_classes.base_data_handler - INFO - Populating ability data with 9 fields...
2025-06-27 11:12:59,046 - core.base_classes.base_data_handler - INFO - Ability data populated successfully
2025-06-27 11:12:59,047 - editors.ability_editor.ability_data_handlers - INFO - Successfully loaded ability: castle_left
2025-06-27 11:12:59,056 - utils.direct_data_manager - INFO - Ability loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\abilities\Kings Swap.json
2025-06-27 11:12:59,057 - core.base_classes.base_data_handler - INFO - Populating ability data with 12 fields...
2025-06-27 11:12:59,058 - core.base_classes.base_data_handler - INFO - Ability data populated successfully
2025-06-27 11:12:59,059 - editors.ability_editor.ability_data_handlers - INFO - Successfully loaded ability: Kings Swap
2025-06-27 11:13:02,567 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:02,568 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:02,579 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:02,596 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:02,609 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:02,612 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:02,614 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:02,615 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:02,617 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:02,618 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:02,620 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:02,628 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:02,629 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:02,636 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:02,647 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:02,658 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:02,660 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:02,661 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:02,662 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:02,663 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:02,664 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:02,665 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:02,668 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:02,669 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:02,676 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:02,686 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:02,700 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:02,702 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:02,703 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:02,704 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:02,705 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:02,706 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:02,707 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:02,711 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:02,712 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:02,719 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:02,732 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:02,745 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:02,747 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:02,748 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:02,749 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:02,750 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:02,752 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:02,753 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:02,756 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:02,757 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:02,764 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:02,778 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:02,792 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:02,794 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:02,795 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:02,796 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:02,797 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:02,798 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:02,799 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:02,820 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:02,821 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:02,837 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:02,856 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:02,873 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:02,875 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:02,877 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:02,878 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:02,879 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:02,880 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:02,881 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:03,668 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,669 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:03,679 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:03,694 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:03,707 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:03,709 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,710 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:03,711 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:03,712 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:03,714 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:03,715 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:03,719 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,720 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:03,730 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:03,743 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:03,753 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:03,755 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,756 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:03,757 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:03,758 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:03,759 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:03,760 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:03,764 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,765 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:03,771 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:03,782 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:03,795 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:03,797 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,798 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:03,799 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:03,799 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:03,801 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:03,803 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:03,806 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,806 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:03,813 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:03,829 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:03,843 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:03,844 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,845 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:03,846 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:03,847 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:03,848 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:03,849 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:03,852 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,853 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:03,860 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:03,871 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:03,884 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:03,886 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,888 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:03,889 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:03,891 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:03,892 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:03,893 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:03,896 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,897 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:03,904 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:03,919 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:03,934 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:03,936 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,940 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:03,942 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:03,944 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:03,945 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:03,947 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:03,952 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,953 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:03,964 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:03,981 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:03,994 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:03,997 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:03,999 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:04,001 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:04,003 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:04,004 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:04,005 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:04,696 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:04,697 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:04,724 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:04,776 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:04,796 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:04,798 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:04,800 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:04,801 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:04,803 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:04,805 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:04,807 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:04,812 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:04,814 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:04,829 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:04,850 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:04,871 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:04,874 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:04,875 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:04,877 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:04,879 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:04,881 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:04,883 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:04,964 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:04,965 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:04,977 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:05,005 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:05,033 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:05,035 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:05,037 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:05,038 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:05,040 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:05,042 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:05,044 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:06,093 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:06,094 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:06,100 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:06,115 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:06,125 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:06,127 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:06,128 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:06,129 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:06,130 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:06,131 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:06,132 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:13:07,560 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:07,563 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:13:07,571 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Bishop
2025-06-27 11:13:07,594 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:13:07,612 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:13:07,615 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:13:07,616 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:13:07,618 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:13:07,619 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:13:07,620 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:13:07,621 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:16:15,387 - utils.utils - INFO - Logging initialized with level: INFO
2025-06-27 11:16:15,462 - core.ui.visual_feedback_integration - INFO - Main window enhanced with visual feedback
2025-06-27 11:16:17,300 - __main__ - INFO - Creating piece editor...
2025-06-27 11:16:17,335 - editors.piece_editor.piece_icon_manager - INFO - Icon UI created successfully
2025-06-27 11:16:17,377 - editors.piece_editor.piece_ui_components - INFO - Main UI initialized successfully
2025-06-27 11:16:17,379 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:16:17,380 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:16:17,381 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:16:17,382 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:16:17,383 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:16:17,384 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:16:17,384 - editors.piece_editor.piece_editor_main - INFO - Resetting piece editor form...
2025-06-27 11:16:17,385 - editors.piece_editor.piece_movement_manager - INFO - Movement pattern reset to default (orthogonal)
2025-06-27 11:16:17,386 - editors.piece_editor.piece_promotion_manager - INFO - Promotions reset to default state
2025-06-27 11:16:17,386 - editors.piece_editor.piece_icon_manager - INFO - Icons reset to default state
2025-06-27 11:16:17,391 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:16:17,392 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:16:17,392 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:16:17,393 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:16:17,394 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:16:17,395 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:16:17,395 - editors.piece_editor.piece_editor_main - INFO - Piece editor form reset completed
2025-06-27 11:16:17,396 - editors.piece_editor.piece_editor_main - INFO - Piece editor initialized successfully
2025-06-27 11:16:17,398 - core.workflow.undo_redo_system - INFO - Undo/Redo system initialized
2025-06-27 11:16:17,398 - core.workflow.workflow_integration - INFO - Undo/Redo system integrated
2025-06-27 11:16:17,400 - core.workflow.workflow_integration - INFO - Enhanced keyboard shortcuts integrated
2025-06-27 11:16:17,401 - core.workflow.workflow_integration - INFO - Auto-save system integrated
2025-06-27 11:16:17,403 - core.workflow.template_system - INFO - Loaded 8 piece templates and 7 ability templates
2025-06-27 11:16:17,404 - core.workflow.workflow_integration - INFO - Template system integrated
2025-06-27 11:16:17,404 - core.workflow.workflow_integration - INFO - Workflow integration complete for PieceEditorWindow
2025-06-27 11:16:17,405 - core.workflow.workflow_integration - INFO - Workflow optimization integrated into PieceEditorWindow
2025-06-27 11:16:17,406 - core.workflow.workflow_integration - INFO - Workflow menu added
2025-06-27 11:16:17,413 - core.ui.visual_feedback_integration - INFO - Visual feedback enhancements applied to PieceEditorWindow
2025-06-27 11:16:17,413 - core.ui.visual_feedback_integration - INFO - Piece editor enhanced with visual feedback
2025-06-27 11:16:17,414 - __main__ - INFO - Piece editor created and integrated successfully
2025-06-27 11:16:20,603 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:16:20,604 - core.base_classes.base_data_handler - INFO - Populating piece data with 19 fields...
2025-06-27 11:16:20,615 - editors.piece_editor.piece_data_handlers - INFO - Loaded movement pattern: Knight
2025-06-27 11:16:20,631 - core.base_classes.base_data_handler - INFO - Piece data populated successfully
2025-06-27 11:16:20,643 - editors.piece_editor.piece_data_handlers - INFO - Piece data loaded successfully using base class
2025-06-27 11:16:20,646 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Bishop.json
2025-06-27 11:16:20,647 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\King.json
2025-06-27 11:16:20,649 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Knight.json
2025-06-27 11:16:20,650 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Pawn.json
2025-06-27 11:16:20,652 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Queen.json
2025-06-27 11:16:20,653 - utils.direct_data_manager - INFO - Piece loaded successfully: C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1 - Copy (2)\data\pieces\Rook.json
2025-06-27 11:21:14,947 - core.performance.editor_integration - WARNING - High memory usage detected: 85.0% of system memory in use
2025-06-27 11:29:17,742 - __main__ - INFO - Creating ability editor...
2025-06-27 11:29:17,847 - editors.ability_editor.ability_ui_components - INFO - Main UI initialized successfully
2025-06-27 11:29:17,852 - editors.ability_editor.ability_tag_managers - INFO - Tag UI created successfully
2025-06-27 11:29:17,857 - editors.ability_editor.ability_data_handlers - INFO - Reset ability editor to defaults
2025-06-27 11:29:17,858 - editors.ability_editor.ability_editor_main - INFO - Ability editor initialized successfully
2025-06-27 11:29:17,861 - core.workflow.undo_redo_system - INFO - Undo/Redo system initialized
2025-06-27 11:29:17,862 - core.workflow.workflow_integration - INFO - Undo/Redo system integrated
2025-06-27 11:29:17,865 - core.workflow.workflow_integration - INFO - Enhanced keyboard shortcuts integrated
2025-06-27 11:29:17,868 - core.workflow.workflow_integration - INFO - Auto-save system integrated
2025-06-27 11:29:17,876 - core.workflow.template_system - INFO - Loaded 8 piece templates and 7 ability templates
2025-06-27 11:29:17,877 - core.workflow.workflow_integration - INFO - Template system integrated
2025-06-27 11:29:17,878 - core.workflow.workflow_integration - INFO - Workflow integration complete for AbilityEditorWindow
2025-06-27 11:29:17,879 - core.workflow.workflow_integration - INFO - Workflow optimization integrated into AbilityEditorWindow
2025-06-27 11:29:17,890 - core.workflow.workflow_integration - INFO - Workflow menu added
2025-06-27 11:29:17,900 - core.ui.visual_feedback_integration - INFO - Visual feedback enhancements applied to AbilityEditorWindow
2025-06-27 11:29:17,900 - core.ui.visual_feedback_integration - INFO - Ability editor enhanced with visual feedback
2025-06-27 11:29:17,902 - __main__ - INFO - Ability editor created and integrated successfully
2025-06-27 12:21:15,247 - core.performance.editor_integration - WARNING - High memory usage detected: 87.3% of system memory in use
