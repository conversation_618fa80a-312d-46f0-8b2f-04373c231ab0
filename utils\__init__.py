"""
Utility modules for Adventure Chess

Location: utils/

Contains general utilities, validators, and helper functions:
- utils.py: Core utility functions (file I/O, logging, validation)
- simple_bridge.py: Data loading interface for UI components
- readable_json_formatter.py: Custom JSON formatting for movement patterns
- direct_data_manager.py: Direct data access utilities
- batch_update.py: Batch operations for data management

Used by: All modules for common operations and data access
"""
