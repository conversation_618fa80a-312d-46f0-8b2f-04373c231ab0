"""
Utility for formatting JSON with readable movement patterns
"""

import json
import re
from typing import Any, Dict


class ReadableMovementPatternEncoder(json.JSONEncoder):
    """Custom JSON encoder that formats movement patterns in a readable way"""

    def encode(self, obj):
        """Override encode to format movement patterns"""
        # Get the standard JSON encoding first
        json_str = super().encode(obj)

        # Post-process to format movement patterns compactly
        json_str = self._format_movement_patterns(json_str)

        return json_str

    def _format_movement_patterns(self, json_str: str) -> str:
        """Format movement patterns in the JSON string to be compact"""
        try:
            # Parse the JSON to work with the actual data structure
            data = json.loads(json_str)

            # Process the data to format patterns
            formatted_data = self._process_patterns_recursive(data)

            # Convert back to JSON with custom pattern formatting
            result = self._custom_json_dump(formatted_data)
            return result

        except Exception as e:
            # If formatting fails, return original JSON
            print(f"Warning: Failed to format movement patterns: {e}")
            return json_str

    def _process_patterns_recursive(self, obj):
        """Recursively process object to find and mark patterns for formatting"""
        if isinstance(obj, dict):
            result = {}
            for key, value in obj.items():
                if key == "pattern" and isinstance(value, list) and len(value) > 0:
                    # Check if this is a movement pattern (list of lists with numbers)
                    if isinstance(value[0], list) and all(isinstance(row, list) for row in value):
                        # Mark this pattern for compact formatting
                        result[key] = {"__COMPACT_PATTERN__": value}
                    else:
                        result[key] = self._process_patterns_recursive(value)
                else:
                    result[key] = self._process_patterns_recursive(value)
            return result
        elif isinstance(obj, list):
            return [self._process_patterns_recursive(item) for item in obj]
        else:
            return obj

    def _custom_json_dump(self, obj, indent=2):
        """Custom JSON dump that handles compact pattern formatting"""
        json_str = json.dumps(obj, indent=indent, ensure_ascii=False)

        # Replace compact pattern markers with actual compact format
        def replace_compact_pattern(match):
            pattern_json = match.group(1)
            try:
                pattern = json.loads(pattern_json)
                # Format as compact arrays: [[0,0,1,0,0,0,0,0], [0,1,0,1,0,0,0,0], ...]
                compact_rows = []
                for row in pattern:
                    compact_row = '[' + ','.join(str(val) for val in row) + ']'
                    compact_rows.append(compact_row)
                return '[' + ', '.join(compact_rows) + ']'
            except:
                return match.group(0)  # Return original if parsing fails

        # Find and replace compact pattern markers
        pattern = r'\{\s*"__COMPACT_PATTERN__":\s*(\[[\s\S]*?\])\s*\}'
        json_str = re.sub(pattern, replace_compact_pattern, json_str, flags=re.MULTILINE | re.DOTALL)

        return json_str


def save_with_readable_patterns(data: Dict[str, Any], filepath: str) -> None:
    """Save data to JSON file with readable movement pattern formatting"""
    try:
        # Use json.dumps with custom encoder, then write to file
        json_str = json.dumps(data, indent=2, ensure_ascii=False, cls=ReadableMovementPatternEncoder)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(json_str)
    except Exception as e:
        # Fallback to standard JSON if custom formatting fails
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Readable formatting failed, using standard JSON: {e}")
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)


def format_movement_pattern_for_display(pattern: list, piece_name: str = "") -> str:
    """Format a movement pattern for display purposes in the user's requested format"""
    if not pattern or not isinstance(pattern, list):
        return "No pattern"

    try:
        formatted_lines = []
        for row in pattern:
            if isinstance(row, list):
                # Convert boolean values to numbers for readability
                converted_row = []
                for val in row:
                    if val is True:
                        converted_row.append('1')
                    elif val is False:
                        converted_row.append('0')
                    else:
                        converted_row.append(str(val))
                formatted_lines.append('[' + ','.join(converted_row) + ']')

        # Format in the user's requested style: "Piece Name; [row1], [row2], [row3], etc"
        if piece_name:
            return f"{piece_name}; " + ', '.join(formatted_lines)
        else:
            return ', '.join(formatted_lines)
    except Exception as e:
        return f"Error formatting pattern: {e}"
