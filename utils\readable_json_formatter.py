"""
Utility for formatting JSON with readable movement patterns
"""

import json
import re
from typing import Any, Dict


class ReadableMovementPatternEncoder(json.JSONEncoder):
    """Custom JSON encoder that formats movement patterns in a readable way"""
    
    def encode(self, obj):
        """Override encode to format movement patterns"""
        # First get the standard JSON encoding
        json_str = super().encode(obj)
        
        # Format movement patterns to be more readable
        json_str = self._format_movement_patterns(json_str)
        
        return json_str
    
    def _format_movement_patterns(self, json_str: str) -> str:
        """Format movement patterns in the JSON string to be more readable"""
        try:
            # Find movement pattern sections and format them
            # Look for "pattern": [ followed by nested arrays
            pattern = r'"pattern":\s*\[\s*(\[[\s\S]*?\])\s*\]'

            def format_pattern_match(match):
                pattern_content = match.group(1)

                # Extract individual rows (arrays)
                row_pattern = r'\[\s*((?:(?:true|false|[0-9]+)(?:\s*,\s*)?)*)\s*\]'
                rows = re.findall(row_pattern, pattern_content)

                if not rows:
                    return match.group(0)  # Return original if parsing fails

                # Format each row on its own line with compact formatting
                formatted_rows = []
                for row_content in rows:
                    if row_content.strip():
                        # Clean up the row content and format compactly
                        values = [v.strip() for v in row_content.split(',') if v.strip()]
                        if values:
                            # Convert boolean values to numbers for readability
                            converted_values = []
                            for val in values:
                                if val.lower() == 'true':
                                    converted_values.append('1')  # true = 1
                                elif val.lower() == 'false':
                                    converted_values.append('0')  # false = 0
                                else:
                                    converted_values.append(val)
                            formatted_row = '[' + ','.join(converted_values) + ']'
                            formatted_rows.append(formatted_row)

                if formatted_rows:
                    # Create the readable format with proper indentation - each row on one line
                    formatted_pattern = '"pattern": [\n'
                    for i, row in enumerate(formatted_rows):
                        formatted_pattern += f'        {row}'
                        if i < len(formatted_rows) - 1:
                            formatted_pattern += ','
                        formatted_pattern += '\n'
                    formatted_pattern += '      ]'
                    return formatted_pattern

                return match.group(0)  # Return original if formatting fails

            # Apply the formatting
            formatted_json = re.sub(pattern, format_pattern_match, json_str, flags=re.MULTILINE | re.DOTALL)

            return formatted_json

        except Exception as e:
            # If formatting fails, return original JSON
            print(f"Warning: Failed to format movement patterns: {e}")
            return json_str


def save_with_readable_patterns(data: Dict[str, Any], filepath: str) -> None:
    """Save data to JSON file with readable movement pattern formatting"""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, cls=ReadableMovementPatternEncoder)
    except Exception as e:
        # Fallback to standard JSON if custom formatting fails
        print(f"Warning: Readable formatting failed, using standard JSON: {e}")
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)


def format_movement_pattern_for_display(pattern: list, piece_name: str = "") -> str:
    """Format a movement pattern for display purposes in the user's requested format"""
    if not pattern or not isinstance(pattern, list):
        return "No pattern"

    try:
        formatted_lines = []
        for row in pattern:
            if isinstance(row, list):
                # Convert boolean values to numbers for readability
                converted_row = []
                for val in row:
                    if val is True:
                        converted_row.append('1')
                    elif val is False:
                        converted_row.append('0')
                    else:
                        converted_row.append(str(val))
                formatted_lines.append('[' + ','.join(converted_row) + ']')

        # Format in the user's requested style: "Piece Name; [row1], [row2], [row3], etc"
        if piece_name:
            return f"{piece_name}; " + ', '.join(formatted_lines)
        else:
            return ', '.join(formatted_lines)
    except Exception as e:
        return f"Error formatting pattern: {e}"
