"""
Unified Data Management System for Adventure Chess Creator

This module provides a single, streamlined data management system that combines:
- Validation capabilities from PydanticDataManager
- Performance optimizations from DirectDataManager  
- Base functionality from BaseDataManager
- Integrated error handling and caching

This replaces the previous multiple data manager approach with a unified system.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any, Union, Callable
from pathlib import Path

from schemas.piece_schema import Piece
from schemas.ability_schema import Ability
from config import PIECES_DIR, ABILITIES_DIR, PIECE_EXTENSION, ABILITY_EXTENSION
from core.error_handling import (
    error_handler, ErrorSeverity, ErrorCategory,
    data_load_handler, data_save_handler, data_delete_handler
)

logger = logging.getLogger(__name__)


class UnifiedDataManager:
    """
    Single source of truth for all data management operations.
    
    Combines validation, performance, and base functionality into one streamlined system.
    Routes operations intelligently based on context and requirements.
    """
    
    def __init__(self):
        # Unified caching system
        self.piece_cache: Dict[str, Piece] = {}
        self.ability_cache: Dict[str, Ability] = {}
        self.metadata_cache: Dict[str, Dict] = {}
        
        # Error tracking
        self.error_log: List[str] = []
        
        # Performance tracking
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Change listeners for backward compatibility
        self._change_listeners: List[Callable] = []
        
        logger.info("Unified Data Manager initialized")
    
    # ========== UNIFIED PIECE OPERATIONS ==========

    @data_load_handler()
    def load_piece(self, filename: str, validate: bool = True) -> Tuple[Optional[Piece], Optional[str]]:
        """
        Load a piece with intelligent routing between validation and performance modes.
        
        Args:
            filename: Piece filename
            validate: Whether to use Pydantic validation (True) or direct loading (False)
            
        Returns:
            Tuple of (piece_model, error_message)
        """
        # Normalize filename
        if not filename.endswith(PIECE_EXTENSION):
            filename += PIECE_EXTENSION

        filepath = Path(PIECES_DIR) / filename

        if not filepath.exists():
            return None, f"Piece file not found: {filename}"

        # Check cache first
        cache_key = str(filepath)
        if cache_key in self.piece_cache:
            self.cache_hits += 1
            logger.debug(f"Loaded piece from cache: {filename}")
            return self.piece_cache[cache_key], None

        self.cache_misses += 1

        # Load and parse JSON
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if validate:
            # Use Pydantic validation for user operations
            piece = Piece.from_legacy_dict(data)

            # Validate the piece
            try:
                piece.model_validate(piece.model_dump())
            except Exception as e:
                return None, f"Validation failed for piece {filename}: {e}"
        else:
            # Direct loading for performance-critical operations
            piece = Piece.from_legacy_dict(data)

        # Cache the result
        self.piece_cache[cache_key] = piece

        logger.debug(f"Successfully loaded piece: {filename}")
        return piece, None
    
    @data_save_handler()
    def save_piece(self, piece: Piece, filename: Optional[str] = None, validate: bool = True) -> Tuple[bool, Optional[str]]:
        """
        Save a piece with intelligent validation and error handling.
        
        Args:
            piece: Piece model to save
            filename: Optional filename (uses piece.name if not provided)
            validate: Whether to validate before saving
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Determine filename
            if filename is None:
                filename = piece.name
            
            # Clean and normalize filename
            filename = self._clean_filename(filename)
            if not filename.endswith(PIECE_EXTENSION):
                filename += PIECE_EXTENSION
            
            filepath = Path(PIECES_DIR) / filename
            
            # Validate if requested
            if validate:
                try:
                    piece.model_validate(piece.model_dump())
                except Exception as e:
                    return False, f"Validation failed: {e}"
            
            # Convert to legacy format for file storage
            data = piece.to_legacy_dict()
            
            # Ensure directories exist
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            # Save to file with readable formatting
            from utils.readable_json_formatter import save_with_readable_patterns
            save_with_readable_patterns(data, filepath)
            
            # Update cache
            cache_key = str(filepath)
            self.piece_cache[cache_key] = piece
            
            # Notify change listeners
            self._notify_change_listeners('piece_saved', filename, piece)
            
            logger.info(f"Successfully saved piece: {piece.name} to {filename}")
            return True, None
            
        except Exception as e:
            error_context = error_handler.handle_error(
                e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.DATA_SAVING,
                operation=f"save_piece({piece.name})",
                file_path=str(filepath) if 'filepath' in locals() else filename
            )
            error_msg = f"Error saving piece {piece.name}: {e}"
            self.error_log.append(error_msg)
            return False, error_msg
    
    def list_pieces(self, use_cache: bool = True) -> List[str]:
        """
        List all available piece files.
        
        Args:
            use_cache: Whether to use cached metadata
            
        Returns:
            List of piece filenames
        """
        try:
            if use_cache and 'piece_list' in self.metadata_cache:
                return self.metadata_cache['piece_list']
            
            if not os.path.exists(PIECES_DIR):
                return []
            
            pieces = [f for f in os.listdir(PIECES_DIR) if f.endswith(PIECE_EXTENSION)]
            
            # Cache the result
            self.metadata_cache['piece_list'] = pieces
            
            return pieces
            
        except Exception as e:
            error_handler.handle_error(
                e,
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.DATA_LOADING,
                operation="list_pieces"
            )
            return []
    
    def delete_piece(self, filename: str) -> Tuple[bool, Optional[str]]:
        """
        Delete a piece file and remove from cache.
        
        Args:
            filename: Piece filename to delete
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Normalize filename
            if not filename.endswith(PIECE_EXTENSION):
                filename += PIECE_EXTENSION
            
            filepath = Path(PIECES_DIR) / filename
            
            if not filepath.exists():
                return False, f"Piece file not found: {filename}"
            
            # Remove file
            filepath.unlink()
            
            # Remove from cache
            cache_key = str(filepath)
            if cache_key in self.piece_cache:
                del self.piece_cache[cache_key]
            
            # Clear metadata cache
            if 'piece_list' in self.metadata_cache:
                del self.metadata_cache['piece_list']
            
            # Notify change listeners
            self._notify_change_listeners('piece_deleted', filename, None)
            
            logger.info(f"Successfully deleted piece: {filename}")
            return True, None
            
        except Exception as e:
            error_context = error_handler.handle_error(
                e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.DATA_DELETION,
                operation=f"delete_piece({filename})",
                file_path=str(filepath) if 'filepath' in locals() else filename
            )
            return False, str(e)

    # ========== UNIFIED ABILITY OPERATIONS ==========

    def load_ability(self, filename: str, validate: bool = True) -> Tuple[Optional[Ability], Optional[str]]:
        """
        Load an ability with intelligent routing between validation and performance modes.

        Args:
            filename: Ability filename
            validate: Whether to use Pydantic validation (True) or direct loading (False)

        Returns:
            Tuple of (ability_model, error_message)
        """
        try:
            # Normalize filename
            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION

            filepath = Path(ABILITIES_DIR) / filename

            if not filepath.exists():
                return None, f"Ability file not found: {filename}"

            # Check cache first
            cache_key = str(filepath)
            if cache_key in self.ability_cache:
                self.cache_hits += 1
                logger.debug(f"Loaded ability from cache: {filename}")
                return self.ability_cache[cache_key], None

            self.cache_misses += 1

            # Load and parse JSON
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if validate:
                # Use Pydantic validation for user operations
                ability = Ability.from_legacy_dict(data)

                # Validate the ability
                try:
                    ability.model_validate(ability.model_dump())
                except Exception as e:
                    return None, f"Validation failed for ability {filename}: {e}"
            else:
                # Direct loading for performance-critical operations
                ability = Ability.from_legacy_dict(data)

            # Cache the result
            self.ability_cache[cache_key] = ability

            logger.debug(f"Successfully loaded ability: {filename}")
            return ability, None

        except Exception as e:
            error_context = error_handler.handle_error(
                e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.DATA_LOADING,
                operation=f"load_ability({filename})",
                file_path=str(filepath) if 'filepath' in locals() else filename
            )
            return None, str(e)

    def save_ability(self, ability: Ability, filename: Optional[str] = None, validate: bool = True) -> Tuple[bool, Optional[str]]:
        """
        Save an ability with intelligent validation and error handling.

        Args:
            ability: Ability model to save
            filename: Optional filename (uses ability.name if not provided)
            validate: Whether to validate before saving

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Determine filename
            if filename is None:
                filename = ability.name

            # Clean and normalize filename
            filename = self._clean_filename(filename)
            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION

            filepath = Path(ABILITIES_DIR) / filename

            # Validate if requested
            if validate:
                try:
                    ability.model_validate(ability.model_dump())
                except Exception as e:
                    return False, f"Validation failed: {e}"

            # Convert to legacy format for file storage
            data = ability.to_legacy_dict()

            # Ensure directories exist
            filepath.parent.mkdir(parents=True, exist_ok=True)

            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            # Update cache
            cache_key = str(filepath)
            self.ability_cache[cache_key] = ability

            # Notify change listeners
            self._notify_change_listeners('ability_saved', filename, ability)

            logger.info(f"Successfully saved ability: {ability.name} to {filename}")
            return True, None

        except Exception as e:
            error_context = error_handler.handle_error(
                e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.DATA_SAVING,
                operation=f"save_ability({ability.name})",
                file_path=str(filepath) if 'filepath' in locals() else filename
            )
            error_msg = f"Error saving ability {ability.name}: {e}"
            self.error_log.append(error_msg)
            return False, error_msg

    def list_abilities(self, use_cache: bool = True) -> List[str]:
        """
        List all available ability files.

        Args:
            use_cache: Whether to use cached metadata

        Returns:
            List of ability filenames
        """
        try:
            if use_cache and 'ability_list' in self.metadata_cache:
                return self.metadata_cache['ability_list']

            if not os.path.exists(ABILITIES_DIR):
                return []

            abilities = [f for f in os.listdir(ABILITIES_DIR) if f.endswith(ABILITY_EXTENSION)]

            # Cache the result
            self.metadata_cache['ability_list'] = abilities

            return abilities

        except Exception as e:
            error_handler.handle_error(
                e,
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.DATA_LOADING,
                operation="list_abilities"
            )
            return []

    def delete_ability(self, filename: str) -> Tuple[bool, Optional[str]]:
        """
        Delete an ability file and remove from cache.

        Args:
            filename: Ability filename to delete

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Normalize filename
            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION

            filepath = Path(ABILITIES_DIR) / filename

            if not filepath.exists():
                return False, f"Ability file not found: {filename}"

            # Remove file
            filepath.unlink()

            # Remove from cache
            cache_key = str(filepath)
            if cache_key in self.ability_cache:
                del self.ability_cache[cache_key]

            # Clear metadata cache
            if 'ability_list' in self.metadata_cache:
                del self.metadata_cache['ability_list']

            # Notify change listeners
            self._notify_change_listeners('ability_deleted', filename, None)

            logger.info(f"Successfully deleted ability: {filename}")
            return True, None

        except Exception as e:
            error_context = error_handler.handle_error(
                e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.DATA_DELETION,
                operation=f"delete_ability({filename})",
                file_path=str(filepath) if 'filepath' in locals() else filename
            )
            return False, str(e)

    # ========== UNIFIED CACHE MANAGEMENT ==========

    def clear_cache(self, cache_type: Optional[str] = None):
        """
        Clear cache data.

        Args:
            cache_type: 'pieces', 'abilities', 'metadata', or None for all
        """
        if cache_type is None or cache_type == 'pieces':
            self.piece_cache.clear()
        if cache_type is None or cache_type == 'abilities':
            self.ability_cache.clear()
        if cache_type is None or cache_type == 'metadata':
            self.metadata_cache.clear()

        logger.info(f"Cleared cache: {cache_type or 'all'}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics."""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0

        return {
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'hit_rate_percent': round(hit_rate, 2),
            'pieces_cached': len(self.piece_cache),
            'abilities_cached': len(self.ability_cache),
            'metadata_cached': len(self.metadata_cache)
        }

    # ========== CHANGE LISTENER SYSTEM ==========

    def add_change_listener(self, listener: Callable):
        """Add a change listener for backward compatibility."""
        if listener not in self._change_listeners:
            self._change_listeners.append(listener)

    def remove_change_listener(self, listener: Callable):
        """Remove a change listener."""
        if listener in self._change_listeners:
            self._change_listeners.remove(listener)

    def _notify_change_listeners(self, operation: str, key: str, value: Any):
        """Notify all change listeners of data changes."""
        for listener in self._change_listeners:
            try:
                listener(operation, key, value)
            except Exception as e:
                logger.warning(f"Error in change listener: {e}")

    # ========== UTILITY METHODS ==========

    def _clean_filename(self, filename: str) -> str:
        """Clean filename for safe file operations."""
        # Remove invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')

        # Remove leading/trailing whitespace and dots
        filename = filename.strip(' .')

        # Ensure not empty
        if not filename:
            filename = 'unnamed'

        return filename

    def get_error_log(self) -> List[str]:
        """Get the current error log."""
        return self.error_log.copy()

    def clear_error_log(self):
        """Clear the error log."""
        self.error_log.clear()

    # ========== BATCH OPERATIONS ==========

    def load_all_pieces(self, validate: bool = False) -> Dict[str, Piece]:
        """
        Load all pieces into cache for bulk operations.

        Args:
            validate: Whether to validate each piece

        Returns:
            Dictionary of filename -> Piece mappings
        """
        pieces = {}
        filenames = self.list_pieces(use_cache=False)

        for filename in filenames:
            piece, error = self.load_piece(filename, validate=validate)
            if piece:
                pieces[filename] = piece
            elif error:
                logger.warning(f"Failed to load piece {filename}: {error}")

        return pieces

    def load_all_abilities(self, validate: bool = False) -> Dict[str, Ability]:
        """
        Load all abilities into cache for bulk operations.

        Args:
            validate: Whether to validate each ability

        Returns:
            Dictionary of filename -> Ability mappings
        """
        abilities = {}
        filenames = self.list_abilities(use_cache=False)

        for filename in filenames:
            ability, error = self.load_ability(filename, validate=validate)
            if ability:
                abilities[filename] = ability
            elif error:
                logger.warning(f"Failed to load ability {filename}: {error}")

        return abilities


# ========== GLOBAL INSTANCE ==========

# Create global unified data manager instance
unified_data_manager = UnifiedDataManager()


# ========== BACKWARD COMPATIBILITY ADAPTERS ==========

class PydanticDataManagerAdapter:
    """Adapter to maintain backward compatibility with PydanticDataManager interface."""

    def __init__(self):
        self.manager = unified_data_manager

    def load_piece(self, filename: str) -> Tuple[Optional[Piece], Optional[str]]:
        return self.manager.load_piece(filename, validate=True)

    def save_piece(self, piece: Piece, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        return self.manager.save_piece(piece, filename, validate=True)

    def load_ability(self, filename: str) -> Tuple[Optional[Ability], Optional[str]]:
        return self.manager.load_ability(filename, validate=True)

    def save_ability(self, ability: Ability, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        return self.manager.save_ability(ability, filename, validate=True)

    @property
    def error_log(self) -> List[str]:
        return self.manager.get_error_log()


class DirectDataManagerAdapter:
    """Adapter to maintain backward compatibility with DirectDataManager interface."""

    def __init__(self):
        self.manager = unified_data_manager

    def save_piece_data(self, piece_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        try:
            # Convert dict to Piece model
            piece = Piece.from_legacy_dict(piece_data)
            return self.manager.save_piece(piece, filename, validate=False)
        except Exception as e:
            return False, str(e)

    def load_piece_data(self, filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        piece, error = self.manager.load_piece(filename, validate=False)
        if piece:
            return piece.to_legacy_dict(), None
        return None, error

    def save_ability_data(self, ability_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        try:
            # Convert dict to Ability model
            ability = Ability.from_legacy_dict(ability_data)
            return self.manager.save_ability(ability, filename, validate=False)
        except Exception as e:
            return False, str(e)

    def load_ability_data(self, filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        ability, error = self.manager.load_ability(filename, validate=False)
        if ability:
            return ability.to_legacy_dict(), None
        return None, error


# Create backward compatibility instances
pydantic_data_manager = PydanticDataManagerAdapter()
direct_data_manager = DirectDataManagerAdapter()
