#!/usr/bin/env python3
"""
Comprehensive Security Enhancement System for Adventure Chess Creator

This consolidated module implements comprehensive security measures including:
- File path validation to prevent directory traversal attacks
- Input sanitization for all user data
- Automatic crash recovery mechanisms
- Strengthened Pydantic validation rules
- Secure file operations with proper error handling
- Integration wrappers for existing data managers

Key Features:
1. Path Security: Prevents directory traversal and ensures files stay within allowed directories
2. Input Validation: Comprehensive sanitization of all user inputs
3. Crash Recovery: Automatic backup and recovery mechanisms
4. Enhanced Validation: Stricter Pydantic rules with security considerations
5. Secure File Operations: Safe file handling with proper permissions and validation
6. Secure Data Manager Wrappers: Drop-in replacements for existing data managers

Consolidates functionality from:
- security_enhancements.py (core security features)
- security_integration.py (integration wrappers)
"""

import os
import re
import json
import logging
import hashlib
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List, Union
from datetime import datetime, timedelta

from config import PIECES_DIR, ABILITIES_DIR, DATA_DIR, BASE_DIR

logger = logging.getLogger(__name__)


class SecurityValidator:
    """
    Comprehensive security validation for file operations and user input
    """
    
    # Allowed file extensions
    ALLOWED_EXTENSIONS = {'.json', '.txt', '.log'}
    
    # Maximum file size (10MB)
    MAX_FILE_SIZE = 10 * 1024 * 1024
    
    # Maximum string length for user inputs
    MAX_STRING_LENGTH = 10000
    
    # Allowed characters in filenames (alphanumeric, underscore, hyphen, space, dot)
    FILENAME_PATTERN = re.compile(r'^[a-zA-Z0-9_\-\s\.]+$')
    
    # Dangerous path patterns to block
    DANGEROUS_PATTERNS = [
        '..',           # Directory traversal
        '~',            # Home directory
        '$',            # Environment variables
        '%',            # Environment variables (Windows)
        '|',            # Pipe operator
        ';',            # Command separator
        '&',            # Command separator
        '`',            # Command substitution
        '*',            # Wildcard
        '?',            # Wildcard
    ]
    
    @classmethod
    def validate_file_path(cls, file_path: Union[str, Path], base_dir: Union[str, Path] = DATA_DIR) -> Tuple[bool, Optional[str]]:
        """
        Validate file path to prevent directory traversal and ensure security
        
        Args:
            file_path: Path to validate
            base_dir: Base directory that file must be within
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Convert to Path objects
            file_path = Path(file_path).resolve()
            base_dir = Path(base_dir).resolve()
            
            # Check if file is within allowed base directory
            try:
                file_path.relative_to(base_dir)
            except ValueError:
                return False, f"File path outside allowed directory: {file_path}"
            
            # Check for dangerous patterns (but allow normal path separators)
            path_str = str(file_path)
            for pattern in cls.DANGEROUS_PATTERNS:
                if pattern in path_str:
                    # Special handling for Windows drive letters
                    if pattern == ':' and len(path_str) > 1 and path_str[1] == ':' and path_str[0].isalpha():
                        continue  # Allow Windows drive letters like C:
                    return False, f"Dangerous pattern '{pattern}' found in path"
            
            # Check file extension
            if file_path.suffix.lower() not in cls.ALLOWED_EXTENSIONS:
                return False, f"File extension '{file_path.suffix}' not allowed"
            
            # Check filename validity
            if not cls.FILENAME_PATTERN.match(file_path.name):
                return False, f"Invalid characters in filename: {file_path.name}"
            
            return True, None
            
        except Exception as e:
            return False, f"Path validation error: {str(e)}"
    
    @classmethod
    def sanitize_filename(cls, filename: str) -> str:
        """
        Sanitize filename for safe file system usage
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        if not filename:
            return "unnamed_file"
        
        # Remove dangerous characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # Remove control characters
        sanitized = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', sanitized)
        
        # Limit length
        if len(sanitized) > 100:
            sanitized = sanitized[:100]
        
        # Remove leading/trailing whitespace and dots
        sanitized = sanitized.strip(' .')
        
        # Ensure not empty after sanitization
        if not sanitized:
            sanitized = "unnamed_file"
        
        # Ensure it matches our pattern
        if not cls.FILENAME_PATTERN.match(sanitized):
            # If still invalid, create a safe default
            sanitized = f"file_{hashlib.md5(filename.encode()).hexdigest()[:8]}"
        
        return sanitized
    
    @classmethod
    def validate_user_input(cls, input_data: Any, field_name: str = "input") -> Tuple[bool, Optional[str]]:
        """
        Validate user input for security and safety
        
        Args:
            input_data: Data to validate
            field_name: Name of the field being validated
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Check for None
            if input_data is None:
                return True, None
            
            # String validation
            if isinstance(input_data, str):
                # Check length
                if len(input_data) > cls.MAX_STRING_LENGTH:
                    return False, f"{field_name} exceeds maximum length ({cls.MAX_STRING_LENGTH})"
                
                # Check for script injection patterns
                dangerous_patterns = [
                    '<script',
                    'javascript:',
                    'vbscript:',
                    'onload=',
                    'onerror=',
                    'eval(',
                    'exec(',
                    '__import__',
                    'subprocess',
                    'os.system',
                ]
                
                input_lower = input_data.lower()
                for pattern in dangerous_patterns:
                    if pattern in input_lower:
                        return False, f"Potentially dangerous content in {field_name}: {pattern}"
            
            # Dictionary validation
            elif isinstance(input_data, dict):
                for key, value in input_data.items():
                    is_valid, error = cls.validate_user_input(key, f"{field_name}.key")
                    if not is_valid:
                        return False, error
                    
                    is_valid, error = cls.validate_user_input(value, f"{field_name}.{key}")
                    if not is_valid:
                        return False, error
            
            # List validation
            elif isinstance(input_data, list):
                for i, item in enumerate(input_data):
                    is_valid, error = cls.validate_user_input(item, f"{field_name}[{i}]")
                    if not is_valid:
                        return False, error
            
            return True, None
            
        except Exception as e:
            return False, f"Input validation error for {field_name}: {str(e)}"
    
    @classmethod
    def validate_file_size(cls, file_path: Union[str, Path]) -> Tuple[bool, Optional[str]]:
        """
        Validate file size to prevent resource exhaustion
        
        Args:
            file_path: Path to file to check
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return True, None  # File doesn't exist yet, that's okay
            
            file_size = file_path.stat().st_size
            
            if file_size > cls.MAX_FILE_SIZE:
                return False, f"File size ({file_size} bytes) exceeds maximum allowed ({cls.MAX_FILE_SIZE} bytes)"
            
            return True, None
            
        except Exception as e:
            return False, f"File size validation error: {str(e)}"


class CrashRecoveryManager:
    """
    Automatic crash recovery and backup management
    """
    
    def __init__(self):
        self.backup_dir = Path(DATA_DIR) / "backups"
        self.recovery_dir = Path(DATA_DIR) / "recovery"
        self.auto_save_interval = 300  # 5 minutes
        self.max_backups = 10
        
        # Ensure directories exist
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.recovery_dir.mkdir(parents=True, exist_ok=True)
    
    def create_backup(self, source_path: Union[str, Path], backup_type: str = "manual") -> Tuple[bool, Optional[str]]:
        """
        Create backup of a file or directory
        
        Args:
            source_path: Path to backup
            backup_type: Type of backup (manual, auto, crash)
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            source_path = Path(source_path)
            
            if not source_path.exists():
                return False, f"Source path does not exist: {source_path}"
            
            # Create timestamped backup name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{source_path.stem}_{backup_type}_{timestamp}{source_path.suffix}"
            backup_path = self.backup_dir / backup_name
            
            # Create backup
            if source_path.is_file():
                import shutil
                shutil.copy2(source_path, backup_path)
            else:
                import shutil
                shutil.copytree(source_path, backup_path)
            
            # Clean up old backups
            self._cleanup_old_backups(source_path.stem)
            
            logger.info(f"Backup created: {backup_path}")
            return True, None
            
        except Exception as e:
            error_msg = f"Backup creation failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def _cleanup_old_backups(self, file_stem: str):
        """Clean up old backup files, keeping only the most recent ones"""
        try:
            # Find all backups for this file
            backups = []
            for backup_file in self.backup_dir.glob(f"{file_stem}_*"):
                if backup_file.is_file():
                    backups.append((backup_file.stat().st_mtime, backup_file))
            
            # Sort by modification time (newest first)
            backups.sort(reverse=True)
            
            # Remove old backups beyond the limit
            for _, backup_file in backups[self.max_backups:]:
                try:
                    backup_file.unlink()
                    logger.debug(f"Removed old backup: {backup_file}")
                except Exception as e:
                    logger.warning(f"Failed to remove old backup {backup_file}: {e}")
                    
        except Exception as e:
            logger.warning(f"Backup cleanup failed: {e}")

    def create_recovery_point(self, data: Dict[str, Any], data_type: str) -> Tuple[bool, Optional[str]]:
        """
        Create recovery point for current application state

        Args:
            data: Current data to save
            data_type: Type of data (piece, ability, etc.)

        Returns:
            Tuple of (success, error_message)
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            recovery_file = self.recovery_dir / f"{data_type}_recovery_{timestamp}.json"

            # Validate data before saving
            is_valid, error = SecurityValidator.validate_user_input(data, data_type)
            if not is_valid:
                return False, f"Recovery point validation failed: {error}"

            # Save recovery data
            with open(recovery_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': timestamp,
                    'data_type': data_type,
                    'data': data,
                    'version': '1.0.0'
                }, f, indent=2, ensure_ascii=False)

            logger.info(f"Recovery point created: {recovery_file}")
            return True, None

        except Exception as e:
            error_msg = f"Recovery point creation failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def find_recovery_files(self, data_type: Optional[str] = None) -> List[Path]:
        """
        Find available recovery files

        Args:
            data_type: Optional filter by data type

        Returns:
            List of recovery file paths, sorted by timestamp (newest first)
        """
        try:
            pattern = f"{data_type}_recovery_*.json" if data_type else "*_recovery_*.json"
            recovery_files = list(self.recovery_dir.glob(pattern))

            # Sort by modification time (newest first)
            recovery_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)

            return recovery_files

        except Exception as e:
            logger.error(f"Error finding recovery files: {e}")
            return []

    def restore_from_recovery(self, recovery_file: Path) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Restore data from recovery file

        Args:
            recovery_file: Path to recovery file

        Returns:
            Tuple of (recovered_data, error_message)
        """
        try:
            if not recovery_file.exists():
                return None, f"Recovery file not found: {recovery_file}"

            with open(recovery_file, 'r', encoding='utf-8') as f:
                recovery_data = json.load(f)

            # Validate recovery data structure
            required_fields = ['timestamp', 'data_type', 'data']
            for field in required_fields:
                if field not in recovery_data:
                    return None, f"Invalid recovery file: missing {field}"

            # Validate the actual data
            is_valid, error = SecurityValidator.validate_user_input(recovery_data['data'], recovery_data['data_type'])
            if not is_valid:
                return None, f"Recovery data validation failed: {error}"

            logger.info(f"Data restored from recovery file: {recovery_file}")
            return recovery_data['data'], None

        except Exception as e:
            error_msg = f"Recovery restoration failed: {str(e)}"
            logger.error(error_msg)
            return None, error_msg


class SecureDataManager:
    """
    Enhanced data manager with security and reliability features
    """

    def __init__(self):
        self.security_validator = SecurityValidator()
        self.recovery_manager = CrashRecoveryManager()

    def secure_save_file(self, data: Dict[str, Any], file_path: Union[str, Path],
                        create_backup: bool = True) -> Tuple[bool, Optional[str]]:
        """
        Securely save data to file with validation and backup

        Args:
            data: Data to save
            file_path: Target file path
            create_backup: Whether to create backup before saving

        Returns:
            Tuple of (success, error_message)
        """
        try:
            file_path = Path(file_path)

            # Validate file path
            is_valid, error = self.security_validator.validate_file_path(file_path)
            if not is_valid:
                return False, error

            # Validate data
            is_valid, error = self.security_validator.validate_user_input(data, "save_data")
            if not is_valid:
                return False, error

            # Create backup if file exists and backup is requested
            if create_backup and file_path.exists():
                success, error = self.recovery_manager.create_backup(file_path, "auto")
                if not success:
                    logger.warning(f"Backup creation failed: {error}")

            # Ensure directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Write to temporary file first (atomic operation)
            temp_file = file_path.with_suffix(file_path.suffix + '.tmp')

            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            # Validate file size
            is_valid, error = self.security_validator.validate_file_size(temp_file)
            if not is_valid:
                temp_file.unlink()  # Remove temp file
                return False, error

            # Atomic move to final location
            temp_file.replace(file_path)

            logger.info(f"File saved securely: {file_path}")
            return True, None

        except Exception as e:
            error_msg = f"Secure save failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def secure_load_file(self, file_path: Union[str, Path]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Securely load data from file with validation

        Args:
            file_path: Path to file to load

        Returns:
            Tuple of (data, error_message)
        """
        try:
            file_path = Path(file_path)

            # Validate file path
            is_valid, error = self.security_validator.validate_file_path(file_path)
            if not is_valid:
                return None, error

            # Check if file exists
            if not file_path.exists():
                return None, f"File not found: {file_path}"

            # Validate file size
            is_valid, error = self.security_validator.validate_file_size(file_path)
            if not is_valid:
                return None, error

            # Load data
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Validate loaded data
            is_valid, error = self.security_validator.validate_user_input(data, "loaded_data")
            if not is_valid:
                return None, error

            logger.info(f"File loaded securely: {file_path}")
            return data, None

        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON format: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
        except Exception as e:
            error_msg = f"Secure load failed: {str(e)}"
            logger.error(error_msg)
            return None, error_msg


# Global instances for easy access
security_validator = SecurityValidator()
crash_recovery_manager = CrashRecoveryManager()
secure_data_manager = SecureDataManager()


# ========== SECURITY INTEGRATION WRAPPERS ==========

class SecureDirectDataManager:
    """
    Security-enhanced wrapper for DirectDataManager
    """

    @staticmethod
    def save_piece(piece_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Securely save piece data with validation and backup

        Args:
            piece_data: Piece data dictionary
            filename: Optional filename

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Import here to avoid circular imports
            from schemas.data_manager import DirectDataManager
            from config import PIECES_DIR

            # Validate input data
            is_valid, error = security_validator.validate_user_input(piece_data, "piece_data")
            if not is_valid:
                return False, f"Input validation failed: {error}"

            # Sanitize filename if provided
            if filename:
                filename = security_validator.sanitize_filename(filename)

            # Create recovery point before saving
            recovery_success, recovery_error = crash_recovery_manager.create_recovery_point(
                piece_data, "piece"
            )
            if not recovery_success:
                logger.warning(f"Recovery point creation failed: {recovery_error}")

            # Use secure data manager for the actual save
            if not filename:
                filename = piece_data.get('name', 'unnamed_piece')

            filename = security_validator.sanitize_filename(filename)
            if not filename.endswith('.json'):
                filename += '.json'

            file_path = Path(PIECES_DIR) / filename

            return secure_data_manager.secure_save_file(piece_data, file_path)

        except Exception as e:
            error_msg = f"Secure piece save failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    @staticmethod
    def load_piece(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Securely load piece data with validation

        Args:
            filename: Filename to load

        Returns:
            Tuple of (piece_data, error_message)
        """
        try:
            from config import PIECES_DIR

            # Sanitize filename
            filename = security_validator.sanitize_filename(filename)
            if not filename.endswith('.json'):
                filename += '.json'

            file_path = Path(PIECES_DIR) / filename

            # Use secure data manager for loading
            data, error = secure_data_manager.secure_load_file(file_path)
            if error:
                return None, error

            # Validate loaded data
            is_valid, validation_error = security_validator.validate_user_input(data, "piece_data")
            if not is_valid:
                return None, f"Loaded data validation failed: {validation_error}"

            return data, None

        except Exception as e:
            error_msg = f"Secure piece load failed: {str(e)}"
            logger.error(error_msg)
            return None, error_msg

    @staticmethod
    def save_ability(ability_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Securely save ability data with validation and backup

        Args:
            ability_data: Ability data dictionary
            filename: Optional filename

        Returns:
            Tuple of (success, error_message)
        """
        try:
            from config import ABILITIES_DIR

            # Validate input data
            is_valid, error = security_validator.validate_user_input(ability_data, "ability_data")
            if not is_valid:
                return False, f"Input validation failed: {error}"

            # Sanitize filename if provided
            if filename:
                filename = security_validator.sanitize_filename(filename)

            # Create recovery point before saving
            recovery_success, recovery_error = crash_recovery_manager.create_recovery_point(
                ability_data, "ability"
            )
            if not recovery_success:
                logger.warning(f"Recovery point creation failed: {recovery_error}")

            # Use secure data manager for the actual save
            if not filename:
                filename = ability_data.get('name', 'unnamed_ability')

            filename = security_validator.sanitize_filename(filename)
            if not filename.endswith('.json'):
                filename += '.json'

            file_path = Path(ABILITIES_DIR) / filename

            return secure_data_manager.secure_save_file(ability_data, file_path)

        except Exception as e:
            error_msg = f"Secure ability save failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    @staticmethod
    def load_ability(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Securely load ability data with validation

        Args:
            filename: Filename to load

        Returns:
            Tuple of (ability_data, error_message)
        """
        try:
            from config import ABILITIES_DIR

            # Sanitize filename
            filename = security_validator.sanitize_filename(filename)
            if not filename.endswith('.json'):
                filename += '.json'

            file_path = Path(ABILITIES_DIR) / filename

            # Use secure data manager for loading
            data, error = secure_data_manager.secure_load_file(file_path)
            if error:
                return None, error

            # Validate loaded data
            is_valid, validation_error = security_validator.validate_user_input(data, "ability_data")
            if not is_valid:
                return None, f"Loaded data validation failed: {validation_error}"

            return data, None

        except Exception as e:
            error_msg = f"Secure ability load failed: {str(e)}"
            logger.error(error_msg)
            return None, error_msg


class SecurePydanticDataManager:
    """
    Security-enhanced wrapper for PydanticDataManager
    """

    def __init__(self):
        from schemas.data_manager import PydanticDataManager
        self.pydantic_manager = PydanticDataManager()

    def save_piece(self, piece, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Securely save piece with enhanced validation

        Args:
            piece: Pydantic Piece model instance
            filename: Optional filename

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Validate piece data
            piece_dict = piece.model_dump()
            is_valid, error = security_validator.validate_user_input(piece_dict, "piece")
            if not is_valid:
                return False, f"Piece validation failed: {error}"

            # Sanitize filename if provided
            if filename:
                filename = security_validator.sanitize_filename(filename)

            # Create recovery point
            recovery_success, recovery_error = crash_recovery_manager.create_recovery_point(
                piece_dict, "piece_pydantic"
            )
            if not recovery_success:
                logger.warning(f"Recovery point creation failed: {recovery_error}")

            # Use original manager with additional security
            return self.pydantic_manager.save_piece(piece, filename)

        except Exception as e:
            error_msg = f"Secure Pydantic piece save failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def load_piece(self, filename: str):
        """
        Securely load piece with enhanced validation

        Args:
            filename: Filename to load

        Returns:
            Tuple of (piece_model, error_message)
        """
        try:
            # Sanitize filename
            filename = security_validator.sanitize_filename(filename)

            # Use original manager
            return self.pydantic_manager.load_piece(filename)

        except Exception as e:
            error_msg = f"Secure Pydantic piece load failed: {str(e)}"
            logger.error(error_msg)
            return None, error_msg

    def save_ability(self, ability, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Securely save ability with enhanced validation

        Args:
            ability: Pydantic Ability model instance
            filename: Optional filename

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Validate ability data
            ability_dict = ability.model_dump()
            is_valid, error = security_validator.validate_user_input(ability_dict, "ability")
            if not is_valid:
                return False, f"Ability validation failed: {error}"

            # Sanitize filename if provided
            if filename:
                filename = security_validator.sanitize_filename(filename)

            # Create recovery point
            recovery_success, recovery_error = crash_recovery_manager.create_recovery_point(
                ability_dict, "ability_pydantic"
            )
            if not recovery_success:
                logger.warning(f"Recovery point creation failed: {recovery_error}")

            # Use original manager with additional security
            return self.pydantic_manager.save_ability(ability, filename)

        except Exception as e:
            error_msg = f"Secure Pydantic ability save failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def load_ability(self, filename: str):
        """
        Securely load ability with enhanced validation

        Args:
            filename: Filename to load

        Returns:
            Tuple of (ability_model, error_message)
        """
        try:
            # Sanitize filename
            filename = security_validator.sanitize_filename(filename)

            # Use original manager
            return self.pydantic_manager.load_ability(filename)

        except Exception as e:
            error_msg = f"Secure Pydantic ability load failed: {str(e)}"
            logger.error(error_msg)
            return None, error_msg


# ========== GLOBAL SECURE MANAGER INSTANCES ==========

# Global secure manager instances
secure_direct_data_manager = SecureDirectDataManager()
secure_pydantic_data_manager = SecurePydanticDataManager()


# ========== RECOVERY INTEGRATION FUNCTIONS ==========

def get_recovery_options(data_type: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get available recovery options for the user

    Args:
        data_type: Optional filter by data type

    Returns:
        List of recovery options with metadata
    """
    try:
        recovery_files = crash_recovery_manager.find_recovery_files(data_type)

        options = []
        for recovery_file in recovery_files:
            try:
                # Get file metadata
                stat = recovery_file.stat()

                # Parse timestamp from filename
                parts = recovery_file.stem.split('_')
                if len(parts) >= 3:
                    file_data_type = parts[0]
                    timestamp = parts[-1]
                else:
                    file_data_type = "unknown"
                    timestamp = "unknown"

                options.append({
                    'file_path': str(recovery_file),
                    'data_type': file_data_type,
                    'timestamp': timestamp,
                    'size': stat.st_size,
                    'modified': stat.st_mtime
                })

            except Exception as e:
                logger.warning(f"Error processing recovery file {recovery_file}: {e}")

        # Sort by modification time (newest first)
        options.sort(key=lambda x: x['modified'], reverse=True)

        return options

    except Exception as e:
        logger.error(f"Error getting recovery options: {e}")
        return []
