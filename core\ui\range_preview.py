"""
Range Preview Component for Adventure Chess Creator

This module provides enhanced visualization components for range patterns,
offering better visual feedback for range configuration in dialogs and editors.
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QGroupBox, QFrame, QScrollArea, QCheckBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPalette
from typing import List, Dict, Any, Set, Tuple, Optional


class RangePreviewWidget(QWidget):
    """
    Enhanced preview widget for range patterns with visual chess board representation
    """
    
    pattern_changed = pyqtSignal(list)  # Emitted when pattern changes (8x8 grid)
    piece_position_changed = pyqtSignal(list)  # Emitted when piece position changes
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.range_pattern = [[False for _ in range(8)] for _ in range(8)]
        self.piece_position = [3, 3]  # Default center position
        self.include_starting_square = False
        self.continue_off_board = False
        self.grid_buttons = []
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the preview UI with enhanced visualization"""
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Header with title and info
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Range Pattern Preview")
        title_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #e2e8f0;
                padding: 5px;
            }
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Info label for pattern count
        self.info_label = QLabel("0 tiles in range")
        self.info_label.setStyleSheet("""
            QLabel {
                color: #a0aec0;
                font-size: 11px;
                padding: 5px;
            }
        """)
        header_layout.addWidget(self.info_label)
        
        layout.addLayout(header_layout)
        
        # Options checkboxes
        options_layout = QHBoxLayout()
        
        self.starting_square_check = QCheckBox("Include Starting Square")
        self.starting_square_check.setStyleSheet("""
            QCheckBox {
                color: #e2e8f0;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                background-color: #2d3748;
                border: 2px solid #4a5568;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border: 2px solid #388E3C;
                border-radius: 3px;
            }
        """)
        self.starting_square_check.stateChanged.connect(self.on_starting_square_changed)
        options_layout.addWidget(self.starting_square_check)
        
        self.continue_off_board_check = QCheckBox("Continue Off Board")
        self.continue_off_board_check.setStyleSheet(self.starting_square_check.styleSheet())
        self.continue_off_board_check.stateChanged.connect(self.on_continue_off_board_changed)
        options_layout.addWidget(self.continue_off_board_check)
        
        options_layout.addStretch()
        layout.addLayout(options_layout)
        
        # Chess board preview
        self.create_chess_board()
        layout.addWidget(self.board_widget)
        
        # Quick preset buttons
        self.create_preset_buttons()
        layout.addWidget(self.preset_widget)
        
        # Pattern description
        self.description_label = QLabel("Left-click tiles to toggle range. Right-click to move piece position.")
        self.description_label.setWordWrap(True)
        self.description_label.setStyleSheet("""
            QLabel {
                color: #a0aec0;
                font-style: italic;
                padding: 8px;
                background-color: #2d3748;
                border-radius: 4px;
                border: 1px solid #4a5568;
            }
        """)
        layout.addWidget(self.description_label)
        
        self.setLayout(layout)
        self.update_display()
    
    def create_chess_board(self):
        """Create the chess board grid for range visualization"""
        self.board_widget = QGroupBox("Range Grid")
        self.board_widget.setStyleSheet("""
            QGroupBox {
                color: #e2e8f0;
                border: 2px solid #4a5568;
                border-radius: 8px;
                background-color: #2d3748;
                font-weight: bold;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        board_layout = QVBoxLayout()
        
        # Grid container
        grid_container = QWidget()
        self.grid_layout = QGridLayout(grid_container)
        self.grid_layout.setSpacing(1)
        
        # Create 8x8 grid
        self.create_grid()
        
        board_layout.addWidget(grid_container)
        self.board_widget.setLayout(board_layout)
    
    def create_grid(self):
        """Create the 8x8 grid buttons"""
        # Clear existing buttons
        for row in self.grid_buttons:
            for btn in row:
                btn.deleteLater()
        self.grid_buttons.clear()
        
        # Create new buttons
        for r in range(8):
            row = []
            for c in range(8):
                btn = QPushButton()
                btn.setFixedSize(32, 32)
                btn.setCheckable(True)
                btn.clicked.connect(lambda checked, row=r, col=c: self.toggle_range_tile(row, col))
                btn.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                btn.customContextMenuRequested.connect(lambda pos, row=r, col=c: self.move_piece_position(row, col))
                
                # Set initial style
                self.update_tile_style(btn, r, c)
                
                self.grid_layout.addWidget(btn, r, c)
                row.append(btn)
            self.grid_buttons.append(row)
    
    def create_preset_buttons(self):
        """Create quick preset pattern buttons"""
        self.preset_widget = QGroupBox("Quick Patterns")
        self.preset_widget.setStyleSheet("""
            QGroupBox {
                color: #e2e8f0;
                border: 2px solid #4a5568;
                border-radius: 8px;
                background-color: #2d3748;
                font-weight: bold;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        preset_layout = QHBoxLayout()
        
        # Preset buttons with chess piece symbols
        presets = [
            ("♜", "rook", "Orthogonal lines (Rook)"),
            ("♝", "bishop", "Diagonal lines (Bishop)"),
            ("♛", "queen", "All directions (Queen)"),
            ("♞", "knight", "L-shaped moves (Knight)"),
            ("♚", "king", "Adjacent squares (King)"),
            ("🌐", "global", "Entire board"),
            ("Clear", "clear", "Clear all range")
        ]
        
        for symbol, preset_type, tooltip in presets:
            btn = QPushButton(symbol)
            btn.setFixedSize(40, 30)
            btn.setToolTip(tooltip)
            btn.setStyleSheet("""
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    border: 2px solid #4a5568;
                    border-radius: 6px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #3a3a3a, stop:1 #2a2a2a);
                    color: white;
                    padding: 2px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4a4a4a, stop:1 #3a3a3a);
                    border-color: #66aaff;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #1a1a1a, stop:1 #2a2a2a);
                    border-color: #4488cc;
                }
            """)
            btn.clicked.connect(lambda checked, pt=preset_type: self.apply_preset_pattern(pt))
            preset_layout.addWidget(btn)
        
        preset_layout.addStretch()
        self.preset_widget.setLayout(preset_layout)
    
    def toggle_range_tile(self, row: int, col: int):
        """Toggle range tile selection"""
        # Don't allow toggling the piece position
        if [row, col] == self.piece_position:
            return
        
        self.range_pattern[row][col] = not self.range_pattern[row][col]
        self.update_display()
        self.pattern_changed.emit(self.range_pattern)
    
    def move_piece_position(self, row: int, col: int):
        """Move the piece to a new position"""
        self.piece_position = [row, col]
        self.update_display()
        self.piece_position_changed.emit(self.piece_position)
    
    def update_tile_style(self, btn: QPushButton, row: int, col: int):
        """Update the style of a grid tile with enhanced chess board appearance"""
        is_light = (row + col) % 2 == 0
        is_piece_position = [row, col] == self.piece_position
        is_in_range = self.range_pattern[row][col]
        
        if is_piece_position:
            # Piece position
            btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #4CAF50, stop:1 #388E3C);
                    border: 2px solid #2E7D32;
                    border-radius: 4px;
                    font-weight: bold;
                    color: white;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #66BB6A, stop:1 #4CAF50);
                }
            """)
            btn.setText("♔")
            btn.setToolTip("Piece position - Right-click to move")
        elif is_in_range:
            # In range tile
            btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #FF9800, stop:1 #F57C00);
                    border: 2px solid #E65100;
                    border-radius: 4px;
                    font-weight: bold;
                    color: white;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #FFB74D, stop:1 #FF9800);
                }
            """)
            btn.setText("⚡")
            btn.setToolTip("In range - Click to remove from range")
        else:
            # Regular chess board tiles
            if is_light:
                btn.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #f0d9b5, stop:1 #e8d1a8);
                        border: 1px solid #b58863;
                        border-radius: 2px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #f5e4c1, stop:1 #f0d9b5);
                        border: 2px solid #8b4513;
                    }
                """)
            else:
                btn.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #b58863, stop:1 #a67c52);
                        border: 1px solid #8b4513;
                        border-radius: 2px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #c19975, stop:1 #b58863);
                        border: 2px solid #654321;
                    }
                """)
            btn.setText("")
            btn.setToolTip("Click to add to range")
    
    def on_starting_square_changed(self, state):
        """Handle starting square checkbox change"""
        self.include_starting_square = state == Qt.CheckState.Checked.value
        self.update_display()
    
    def on_continue_off_board_changed(self, state):
        """Handle continue off board checkbox change"""
        self.continue_off_board = state == Qt.CheckState.Checked.value
        self.update_display()
    
    def apply_preset_pattern(self, preset_type: str):
        """Apply a preset range pattern"""
        piece_r, piece_c = self.piece_position
        
        # Clear current pattern
        self.range_pattern = [[False for _ in range(8)] for _ in range(8)]
        
        if preset_type == "clear":
            pass  # Already cleared
        elif preset_type == "rook":
            # Orthogonal lines
            for i in range(8):
                if i != piece_r:
                    self.range_pattern[i][piece_c] = True
                if i != piece_c:
                    self.range_pattern[piece_r][i] = True
        elif preset_type == "bishop":
            # Diagonal lines
            for r in range(8):
                for c in range(8):
                    if abs(r - piece_r) == abs(c - piece_c) and [r, c] != [piece_r, piece_c]:
                        self.range_pattern[r][c] = True
        elif preset_type == "queen":
            # Combine rook and bishop
            for r in range(8):
                for c in range(8):
                    if ([r, c] != [piece_r, piece_c] and 
                        (r == piece_r or c == piece_c or abs(r - piece_r) == abs(c - piece_c))):
                        self.range_pattern[r][c] = True
        elif preset_type == "knight":
            # L-shaped moves
            knight_moves = [(-2, -1), (-2, 1), (-1, -2), (-1, 2), (1, -2), (1, 2), (2, -1), (2, 1)]
            for dr, dc in knight_moves:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    self.range_pattern[r][c] = True
        elif preset_type == "king":
            # Adjacent squares
            for dr in [-1, 0, 1]:
                for dc in [-1, 0, 1]:
                    if dr == 0 and dc == 0:
                        continue
                    r, c = piece_r + dr, piece_c + dc
                    if 0 <= r < 8 and 0 <= c < 8:
                        self.range_pattern[r][c] = True
        elif preset_type == "global":
            # Entire board
            for r in range(8):
                for c in range(8):
                    if [r, c] != [piece_r, piece_c]:
                        self.range_pattern[r][c] = True
        
        self.update_display()
        self.pattern_changed.emit(self.range_pattern)
    
    def set_pattern(self, pattern: List[List[bool]]):
        """Set the range pattern"""
        if pattern and len(pattern) == 8 and all(len(row) == 8 for row in pattern):
            self.range_pattern = [row[:] for row in pattern]  # Deep copy
            self.update_display()
    
    def get_pattern(self) -> List[List[bool]]:
        """Get the current range pattern"""
        return [row[:] for row in self.range_pattern]  # Deep copy
    
    def set_piece_position(self, position: List[int]):
        """Set the piece position"""
        if len(position) == 2 and 0 <= position[0] < 8 and 0 <= position[1] < 8:
            self.piece_position = position[:]
            self.update_display()
    
    def get_piece_position(self) -> List[int]:
        """Get the current piece position"""
        return self.piece_position[:]
    
    def update_display(self):
        """Update the visual display"""
        # Count tiles in range
        count = sum(sum(row) for row in self.range_pattern)
        self.info_label.setText(f"{count} tile{'s' if count != 1 else ''} in range")
        
        # Update tile styles
        if self.grid_buttons:
            for r, row in enumerate(self.grid_buttons):
                for c, btn in enumerate(row):
                    self.update_tile_style(btn, r, c)
                    # Update checked state
                    btn.setChecked(self.range_pattern[r][c])
    
    def clear_pattern(self):
        """Clear the range pattern"""
        self.range_pattern = [[False for _ in range(8)] for _ in range(8)]
        self.update_display()
        self.pattern_changed.emit(self.range_pattern)
