"""
Grid Components for Adventure Chess Creator

This module contains grid-based UI components extracted from ui_shared_components.py:
- GridToggleWidget: Base grid with toggle functionality
- AreaEffectGridWidget: Specialized grid for area effects with target positioning

These components are used for pattern editors, area effect configuration,
and other grid-based interactions throughout the application.
"""

from typing import List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton
)
from PyQt6.QtCore import Qt


class GridToggleWidget(QWidget):
    """
    Reusable grid widget with toggle functionality
    Used for pattern editors, area effect masks, etc.
    """
    
    def __init__(self, rows: int = 8, cols: int = 8, parent=None):
        super().__init__(parent)
        self.rows = rows
        self.cols = cols
        self.grid = [[0 for _ in range(cols)] for _ in range(rows)]
        self.buttons = []
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the grid UI"""
        layout = QVBoxLayout()
        
        # Grid
        grid_layout = QGridLayout()
        self.buttons = []
        
        for row in range(self.rows):
            button_row = []
            for col in range(self.cols):
                btn = QPushButton()
                btn.setFixedSize(30, 30)
                btn.setCheckable(True)
                btn.clicked.connect(lambda checked, r=row, c=col: self.toggle_tile(r, c))
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: #f0f0f0;
                        border: 1px solid #ccc;
                    }
                    QPushButton:checked {
                        background-color: #4caf50;
                        color: white;
                    }
                """)
                grid_layout.addWidget(btn, row, col)
                button_row.append(btn)
            self.buttons.append(button_row)
        
        layout.addLayout(grid_layout)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        clear_btn = QPushButton("Clear All")
        clear_btn.clicked.connect(self.clear_all)
        button_layout.addWidget(clear_btn)
        
        select_all_btn = QPushButton("Select All")
        select_all_btn.clicked.connect(self.select_all)
        button_layout.addWidget(select_all_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def toggle_tile(self, row: int, col: int):
        """Toggle a tile in the grid"""
        if 0 <= row < self.rows and 0 <= col < self.cols:
            self.grid[row][col] = 1 - self.grid[row][col]
            self.buttons[row][col].setChecked(self.grid[row][col] == 1)
            self.update_display()
    
    def clear_all(self):
        """Clear all tiles"""
        for row in range(self.rows):
            for col in range(self.cols):
                self.grid[row][col] = 0
                self.buttons[row][col].setChecked(False)
        self.update_display()
    
    def select_all(self):
        """Select all tiles"""
        for row in range(self.rows):
            for col in range(self.cols):
                self.grid[row][col] = 1
                self.buttons[row][col].setChecked(True)
        self.update_display()
    
    def update_display(self):
        """Update the display - override in subclasses"""
        pass
    
    def get_mask(self) -> List[List[int]]:
        """Get the current grid mask"""
        return [row[:] for row in self.grid]  # Deep copy
    
    def set_mask(self, mask: List[List[int]]):
        """Set the grid mask"""
        if len(mask) == self.rows and all(len(row) == self.cols for row in mask):
            for row in range(self.rows):
                for col in range(self.cols):
                    self.grid[row][col] = mask[row][col]
                    self.buttons[row][col].setChecked(mask[row][col] == 1)
            self.update_display()


class AreaEffectGridWidget(GridToggleWidget):
    """
    Specialized grid widget for area effect masks
    Supports target position and boolean mask
    """
    
    def __init__(self, initial_mask=None, target_pos=None, parent=None):
        super().__init__(8, 8, parent)
        self.target_pos = target_pos or [3, 3]
        
        # Convert boolean mask to integer grid
        if initial_mask:
            for row in range(8):
                for col in range(8):
                    self.grid[row][col] = 1 if initial_mask[row][col] else 0
                    self.buttons[row][col].setChecked(initial_mask[row][col])
        
        self.update_display()
    
    def setup_ui(self):
        """Setup the grid UI with right-click support"""
        super().setup_ui()
        
        # Add right-click support for target position
        for row in range(self.rows):
            for col in range(self.cols):
                btn = self.buttons[row][col]
                btn.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                btn.customContextMenuRequested.connect(
                    lambda pos, r=row, c=col: self.set_target_position(r, c)
                )
    
    def set_target_position(self, row: int, col: int):
        """Set the target position (right-click)"""
        self.target_pos = [row, col]
        self.update_display()
    
    def update_display(self):
        """Update the visual display with target position"""
        for r in range(8):
            for c in range(8):
                btn = self.buttons[r][c]
                
                if [r, c] == self.target_pos:
                    # Target position
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #ff9800;
                            color: white;
                            border: 2px solid #f57c00;
                            font-weight: bold;
                        }
                    """)
                    btn.setText("🎯")
                elif self.grid[r][c]:
                    # Selected tile
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #4caf50;
                            color: white;
                            border: 1px solid #388e3c;
                        }
                    """)
                    btn.setText("")
                else:
                    # Empty tile (dark theme)
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #2d3748;
                            border: 1px solid #4a5568;
                        }
                    """)
                    btn.setText("")
    
    def get_boolean_mask(self):
        """Get the mask as boolean array"""
        return [[bool(self.grid[r][c]) for c in range(8)] for r in range(8)]
    
    def get_target_position(self):
        """Get the target position"""
        return self.target_pos[:]
