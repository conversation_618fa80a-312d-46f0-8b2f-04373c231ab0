"""
Base Data Handler for Adventure Chess Creator

This module provides a unified base class for all data handlers in the application,
consolidating common patterns from PieceDataHandler and AbilityDataHandler.

The BaseDataHandler provides:
- Standardized save/load operations
- Data validation and error handling
- UI data collection and population
- File operations with error recovery
- Integration with SimpleBridge and EditorDataInterface
"""

import logging
import os
from typing import Dict, Any, List, Tuple, Optional, Union
from PyQt6.QtWidgets import QMessageBox

# Lazy imports to avoid circular dependencies
# from utils.simple_bridge import simple_bridge
# from core.interfaces.editor_data_interface import EditorDataInterface

logger = logging.getLogger(__name__)


class BaseDataHandler:
    """
    Base class for all data handlers in Adventure Chess Creator.
    
    Consolidates common patterns from PieceDataHandler and AbilityDataHandler
    to reduce code duplication and provide consistent data handling across
    all editors.
    """
    
    def __init__(self, editor_instance, data_type: str):
        """
        Initialize the base data handler.
        
        Args:
            editor_instance: The editor instance (PieceEditor or AbilityEditor)
            data_type: Type of data being handled ('piece' or 'ability')
        """
        self.editor = editor_instance
        self.data_type = data_type
        # Lazy import to avoid circular dependencies
        from core.interfaces.editor_data_interface import EditorDataInterface
        self.data_interface = EditorDataInterface()
        
        # Data state tracking - use editor as single source of truth
        self.validation_errors = []
        
    # ========== CORE DATA OPERATIONS ==========
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect all data from UI widgets using standardized interface.
        
        Returns:
            Dictionary containing all collected data
        """
        try:
            # Use EditorDataInterface for standardized data collection
            data = self.data_interface.collect_data_from_ui(self.editor, self.data_type)
            
            # Add version information
            data.setdefault('version', '1.0.0')
            
            # Collect type-specific data
            type_specific_data = self.collect_type_specific_data()
            data.update(type_specific_data)
            
            # Ensure required fields exist with defaults
            self.ensure_required_fields(data)
            
            logger.debug(f"Collected {self.data_type} data: {data.get('name', 'Unnamed')}")
            return data
            
        except Exception as e:
            logger.error(f"Error collecting {self.data_type} data: {e}")
            return self.get_default_data()
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate UI widgets with data using standardized interface.

        Args:
            data: Dictionary containing data to populate
        """
        try:
            logger.info(f"Populating {self.data_type} data with {len(data)} fields...")

            # Use EditorDataInterface for standardized UI population
            self.data_interface.populate_ui_from_data(self.editor, data, self.data_type)

            # Handle type-specific data population
            self.populate_type_specific_data(data)

            # Update UI components after loading
            self.post_populate_update()

            # Store current state in editor (single source of truth)
            self.editor.current_data = data.copy()

            logger.info(f"{self.data_type.title()} data populated successfully")
            
        except Exception as e:
            logger.error(f"Error populating {self.data_type} data: {e}")
            raise
    
    def validate_data(self, data: Optional[Dict[str, Any]] = None) -> List[str]:
        """
        Validate data with type-specific validation rules.
        
        Args:
            data: Optional data to validate (uses current data if None)
            
        Returns:
            List of validation error messages
        """
        if data is None:
            data = self.collect_data()
        
        errors = []
        
        # Basic validation
        if not data.get('name', '').strip():
            errors.append(f"{self.data_type.title()} name is required")
        
        # Type-specific validation
        type_errors = self.validate_type_specific_data(data)
        errors.extend(type_errors)
        
        self.validation_errors = errors
        return errors
    
    # ========== FILE OPERATIONS ==========
    
    def save_data(self, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save data to file with validation and error handling.
        
        Args:
            filename: Optional filename (without extension)
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Collect current data
            data = self.collect_data()
            
            # Validate before saving
            validation_errors = self.validate_data(data)
            if validation_errors:
                error_msg = "Validation failed:\n" + "\n".join(validation_errors)
                logger.error(error_msg)
                return False, error_msg
            
            # Save using SimpleBridge (lazy import)
            from utils.simple_bridge import simple_bridge
            if self.data_type == "piece":
                success, error = simple_bridge.save_piece_from_ui(self.editor, filename)
            elif self.data_type == "ability":
                success, error = simple_bridge.save_ability_from_ui(self.editor, filename)
            else:
                return False, f"Unknown data type: {self.data_type}"
            
            if not success:
                logger.error(f"Failed to save {self.data_type}: {error}")
                return False, error
            
            # Update saved state
            self.last_saved_data = data.copy()
            
            # Clear unsaved changes flag
            if hasattr(self.editor, 'clear_unsaved_changes'):
                self.editor.clear_unsaved_changes()
            
            logger.info(f"Successfully saved {self.data_type}: {data.get('name', 'Unnamed')}")
            return True, None
            
        except Exception as e:
            error_msg = f"Error saving {self.data_type}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def load_data(self, filename: str) -> Tuple[bool, Optional[str]]:
        """
        Load data from file with error handling.
        
        Args:
            filename: Name of file to load
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Load using SimpleBridge
            if self.data_type == "piece":
                data, error = simple_bridge.load_piece_for_ui(filename)
            elif self.data_type == "ability":
                data, error = simple_bridge.load_ability_for_ui(filename)
            else:
                return False, f"Unknown data type: {self.data_type}"
            
            if error:
                logger.error(f"Failed to load {self.data_type}: {error}")
                return False, error
            
            # Populate UI with loaded data
            self.populate_data(data)
            
            # Update saved state in editor (single source of truth)
            self.editor.last_saved_data = data.copy()
            
            logger.info(f"Successfully loaded {self.data_type}: {filename}")
            return True, None
            
        except Exception as e:
            error_msg = f"Error loading {self.data_type}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    # ========== STATE MANAGEMENT ==========
    
    def has_unsaved_changes(self) -> bool:
        """Check if current data differs from last saved state"""
        current = self.collect_data()
        return current != getattr(self.editor, 'last_saved_data', {})
    
    def reset_to_defaults(self):
        """Reset to default state - override in subclasses"""
        pass
    
    def post_populate_update(self):
        """Update UI components after data population - override in subclasses"""
        pass
    
    # ========== TYPE-SPECIFIC METHODS (OVERRIDE IN SUBCLASSES) ==========
    
    def collect_type_specific_data(self) -> Dict[str, Any]:
        """Collect type-specific data - override in subclasses"""
        return {}
    
    def populate_type_specific_data(self, data: Dict[str, Any]):
        """Populate type-specific data - override in subclasses"""
        pass
    
    def validate_type_specific_data(self, data: Dict[str, Any]) -> List[str]:
        """Validate type-specific data - override in subclasses"""
        return []
    
    def ensure_required_fields(self, data: Dict[str, Any]):
        """Ensure required fields exist with defaults - override in subclasses"""
        pass
    
    def get_default_data(self) -> Dict[str, Any]:
        """Get default data structure - override in subclasses"""
        return {
            'version': '1.0.0',
            'name': '',
            'description': ''
        }
    
    # ========== UTILITY METHODS ==========
    
    def show_error_message(self, title: str, message: str):
        """Show error message to user"""
        QMessageBox.critical(self.editor, title, message)
    
    def show_validation_errors(self, errors: List[str]):
        """Show validation errors to user"""
        if errors:
            error_msg = "Validation Errors:\n\n" + "\n".join(f"• {error}" for error in errors)
            self.show_error_message("Validation Failed", error_msg)
