{"version": "1.0.0", "name": "Pawn", "description": "Moves forward one square, captures diagonally, with special first move", "role": "Supporter", "can_castle": false, "track_starting_position": true, "color_directional": true, "can_capture": true, "movement": {"type": "custom", "distance": 1, "pattern": [[0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 0, 1, 0, 0, 0], [0, 0, 0, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0]], "piece_position": [2, 3]}, "enable_recharge": false, "max_points": 0, "starting_points": 0, "recharge_type": "turn<PERSON><PERSON><PERSON><PERSON>", "turn_points": 1, "capture_points": 0, "move_points": 0, "committed_recharge_turns": 1, "abilities": ["pawn_movement_2", "en_passant"], "promotions": ["Queen", "Rook", "<PERSON>", "<PERSON>"], "secondary_promotions": []}