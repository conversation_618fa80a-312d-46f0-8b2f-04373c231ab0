#!/usr/bin/env python3
"""
Unified Adjacency Dialog for Adventure Chess
Combines InlinePieceSelector with custom grid pattern system for all adjacency needs
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout,
    QPushButton, QLabel, QGroupBox, QSpinBox, QWidget
)
from PyQt6.QtCore import Qt

# Import shared UI utilities
from core.ui import create_dialog_buttons
from core.ui import InlinePieceSelector, AdjacencyPreviewWidget


class UnifiedAdjacencyDialog(QDialog):
    """
    Unified dialog for all adjacency configuration needs
    Used for both piece editor recharge and ability editor adjacency tags
    """
    
    def __init__(self, parent=None, initial_config=None, dialog_type="adjacency_required"):
        super().__init__(parent)
        self.dialog_type = dialog_type  # "adjacency_required" or "adjacency_recharge"

        if dialog_type == "adjacency_recharge":
            self.setWindowTitle("Adjacency Recharge Configuration")
        else:
            self.setWindowTitle("Adjacency Required Configuration")

        self.setMinimumSize(500, 600)

        # Apply dark theme styling
        self.setStyleSheet("""
            QDialog {
                background-color: #1a202c;
                color: #e2e8f0;
            }
            QLabel {
                color: #e2e8f0;
            }
            QGroupBox {
                color: #e2e8f0;
                border: 1px solid #4a5568;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #2d3748;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: #1a202c;
            }
            QSpinBox {
                background-color: #2d3748;
                color: #e2e8f0;
                border: 1px solid #4a5568;
                border-radius: 4px;
                padding: 4px;
            }
            QPushButton {
                background-color: #2d3748;
                color: #e2e8f0;
                border: 1px solid #4a5568;
                border-radius: 4px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #4a5568;
                border-color: #718096;
            }
            QPushButton:pressed {
                background-color: #1a202c;
            }
        """)

        # Initialize configuration
        self.config = initial_config if initial_config else {
            'pieces': [],
            'distance': 1,
            'pattern': set()  # Set of relative positions (row_offset, col_offset)
        }

        # Initialize adjacency selection tracking
        self.adjacency_selected = set()
        if 'pattern' in self.config:
            self.adjacency_selected = set(self.config['pattern'])

        self.init_ui()
        self.load_config()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()
        
        # Instructions with dark theme styling
        if self.dialog_type == "adjacency_recharge":
            instructions = QLabel("Configure which pieces must be adjacent for recharge to work.")
        else:
            instructions = QLabel("Configure which pieces must be adjacent for this ability to activate.")
        instructions.setWordWrap(True)
        instructions.setStyleSheet("color: #a0aec0; font-style: italic; padding: 10px; background-color: #2d3748; border-radius: 4px; margin: 5px;")
        layout.addWidget(instructions)
        
        # Piece Selector Section
        pieces_group = QGroupBox("Required Adjacent Pieces")
        pieces_layout = QVBoxLayout(pieces_group)
        
        # Use inline piece selector with costs for recharge
        allow_costs = self.dialog_type == "adjacency_recharge"
        self.piece_selector = InlinePieceSelector(
            self, 
            "Adjacent Pieces", 
            allow_costs=allow_costs
        )
        self.piece_selector.pieces_changed.connect(self.on_pieces_changed)
        pieces_layout.addWidget(self.piece_selector)
        
        layout.addWidget(pieces_group)
        
        # Distance and Pattern Section
        pattern_group = QGroupBox("Adjacency Pattern")
        pattern_layout = QVBoxLayout(pattern_group)
        
        # Distance configuration
        distance_layout = QFormLayout()
        
        self.distance_spin = QSpinBox()
        self.distance_spin.setRange(1, 5)
        self.distance_spin.setValue(1)
        self.distance_spin.setToolTip("Maximum distance for adjacency (1=3x3, 2=5x5, etc.)")
        self.distance_spin.valueChanged.connect(self.update_distance)
        distance_layout.addRow("Max Distance:", self.distance_spin)
        
        pattern_layout.addLayout(distance_layout)
        
        # Enhanced adjacency preview widget
        self.adjacency_preview = AdjacencyPreviewWidget(max_distance=5)
        self.adjacency_preview.pattern_changed.connect(self.on_pattern_changed)
        pattern_layout.addWidget(self.adjacency_preview)
        
        layout.addWidget(pattern_group)
        
        # Dialog buttons
        save_btn, cancel_btn = create_dialog_buttons("Save", "Cancel")
        save_btn.clicked.connect(self.accept)
        cancel_btn.clicked.connect(self.reject)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(save_btn)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def update_distance(self):
        """Update the distance in the adjacency preview widget"""
        distance = self.distance_spin.value()
        self.adjacency_preview.set_distance(distance)

    def on_pattern_changed(self, pattern):
        """Handle pattern changes from the adjacency preview widget"""
        self.adjacency_selected = pattern

    def on_pieces_changed(self):
        """Handle piece selection changes"""
        pieces = self.piece_selector.get_pieces()
        self.adjacency_preview.set_required_pieces(pieces)

    
    def load_config(self):
        """Load configuration into the dialog"""
        if 'pieces' in self.config:
            self.piece_selector.set_pieces(self.config['pieces'])
            self.adjacency_preview.set_required_pieces(self.config['pieces'])

        if 'distance' in self.config:
            self.distance_spin.setValue(self.config['distance'])
            self.adjacency_preview.set_distance(self.config['distance'])

        if 'pattern' in self.config:
            self.adjacency_selected = set(self.config['pattern'])
            self.adjacency_preview.set_pattern(self.adjacency_selected)
    
    def get_config(self):
        """Get the current configuration"""
        return {
            'pieces': self.piece_selector.get_pieces(),
            'distance': self.distance_spin.value(),
            'pattern': list(self.adjacency_preview.get_pattern())  # Convert set to list for JSON serialization
        }


def edit_adjacency_config(parent=None, initial_config=None, dialog_type="adjacency_required"):
    """
    Convenience function to edit adjacency configuration
    
    Args:
        parent: Parent widget
        initial_config: Initial configuration dict
        dialog_type: "adjacency_required" or "adjacency_recharge"
    
    Returns:
        Configuration dict if accepted, None if cancelled
    """
    dialog = UnifiedAdjacencyDialog(parent, initial_config, dialog_type)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_config()
    return None


# Legacy compatibility functions
def edit_adjacency_recharge_config(parent=None, initial_config=None):
    """Legacy compatibility for adjacency recharge"""
    return edit_adjacency_config(parent, initial_config, "adjacency_recharge")


def edit_adjacency_required_config(parent=None, initial_config=None):
    """Legacy compatibility for adjacency required"""
    return edit_adjacency_config(parent, initial_config, "adjacency_required")
