"""
Undo/Redo System for Adventure Chess Creator

This module provides comprehensive undo/redo functionality for editors
with command pattern implementation and QUndoStack integration.
"""

import logging
from typing import Dict, Any
from PyQt6.QtGui import QUndoStack, QUndoCommand, QKeySequence

logger = logging.getLogger(__name__)


class EditorCommand(QUndoCommand):
    """Base command for editor operations"""
    
    def __init__(self, editor, description: str):
        super().__init__(description)
        self.editor = editor
        self.old_data = None
        self.new_data = None
    
    def undo(self):
        """Undo the command"""
        if self.old_data is not None:
            self.editor.load_data_from_dict(self.old_data)
            logger.debug(f"Undid: {self.text()}")
    
    def redo(self):
        """Redo the command"""
        if self.new_data is not None:
            self.editor.load_data_from_dict(self.new_data)
            logger.debug(f"Redid: {self.text()}")


class FieldChangeCommand(EditorCommand):
    """Command for field value changes"""
    
    def __init__(self, editor, field_name: str, old_value: Any, new_value: Any):
        super().__init__(editor, f"Change {field_name}")
        self.field_name = field_name
        self.old_value = old_value
        self.new_value = new_value
    
    def undo(self):
        """Undo field change"""
        self.editor.set_field_value(self.field_name, self.old_value)
        logger.debug(f"Undid field change: {self.field_name} = {self.old_value}")
    
    def redo(self):
        """Redo field change"""
        self.editor.set_field_value(self.field_name, self.new_value)
        logger.debug(f"Redid field change: {self.field_name} = {self.new_value}")


class DataChangeCommand(EditorCommand):
    """Command for complete data changes"""
    
    def __init__(self, editor, description: str, old_data: Dict[str, Any], new_data: Dict[str, Any]):
        super().__init__(editor, description)
        self.old_data = old_data.copy() if old_data else {}
        self.new_data = new_data.copy() if new_data else {}


class UndoRedoManager:
    """Manages undo/redo operations for editors"""
    
    def __init__(self, editor):
        self.editor = editor
        self.undo_stack = QUndoStack()
        self.undo_stack.setUndoLimit(50)  # Limit to 50 operations
        
        # Connect to editor's undo/redo actions if they exist
        self.setup_actions()
    
    def setup_actions(self):
        """Setup undo/redo actions"""
        try:
            # Create undo/redo actions
            self.undo_action = self.undo_stack.createUndoAction(self.editor, "Undo")
            self.redo_action = self.undo_stack.createRedoAction(self.editor, "Redo")
            
            # Set shortcuts
            self.undo_action.setShortcut(QKeySequence.StandardKey.Undo)
            self.redo_action.setShortcut(QKeySequence.StandardKey.Redo)
            
            logger.info("Undo/Redo system initialized")
            
        except Exception as e:
            logger.error(f"Error setting up undo/redo actions: {e}")
    
    def push_command(self, command: QUndoCommand):
        """Push a command to the undo stack"""
        self.undo_stack.push(command)
    
    def push_field_change(self, field_name: str, old_value: Any, new_value: Any):
        """Push a field change command"""
        command = FieldChangeCommand(self.editor, field_name, old_value, new_value)
        self.push_command(command)
    
    def push_data_change(self, description: str, old_data: Dict[str, Any], new_data: Dict[str, Any]):
        """Push a data change command"""
        command = DataChangeCommand(self.editor, description, old_data, new_data)
        self.push_command(command)
    
    def clear(self):
        """Clear the undo stack"""
        self.undo_stack.clear()
    
    def can_undo(self) -> bool:
        """Check if undo is available"""
        return self.undo_stack.canUndo()
    
    def can_redo(self) -> bool:
        """Check if redo is available"""
        return self.undo_stack.canRedo()
    
    def get_undo_action(self):
        """Get the undo action for menu integration"""
        return self.undo_action
    
    def get_redo_action(self):
        """Get the redo action for menu integration"""
        return self.redo_action
    
    def get_undo_stack(self):
        """Get the undo stack for advanced operations"""
        return self.undo_stack
