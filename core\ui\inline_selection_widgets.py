#!/usr/bin/env python3
"""
Inline Selection Widgets for Adventure Chess
Enhanced widgets for displaying and managing piece/ability selections directly in the configuration tab
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                            QFrame, QScrollArea, QSizePolicy, QGridLayout, QComboBox,
                            QSpinBox, QCheckBox, QGroupBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPalette
from typing import List, Dict, Any, Optional


class InlinePieceSelector(QWidget):
    """
    Inline piece selector widget that displays selected pieces in a grid layout
    with add/remove functionality without opening separate dialogs
    """
    
    pieces_changed = pyqtSignal()  # Emitted when pieces list changes
    
    def __init__(self, parent=None, title="Pieces", allow_costs=True):
        super().__init__(parent)
        self.title = title
        self.allow_costs = allow_costs
        self.pieces = []  # List of {'piece': str, 'cost': int} dicts
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the enhanced two-column inline piece selector UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # Header with title
        title_label = QLabel(self.title + ":")
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # Main content area with two columns
        main_content = QHBoxLayout()
        main_content.setSpacing(12)

        # LEFT COLUMN: Options and Controls
        left_column = QVBoxLayout()
        left_column.setSpacing(8)

        # Piece selection dropdown
        piece_selection_layout = QHBoxLayout()
        piece_selection_layout.addWidget(QLabel("Piece:"))
        
        self.piece_combo = QComboBox()
        self.piece_combo.setMinimumWidth(120)
        self.piece_combo.addItems(self.get_piece_names())
        piece_selection_layout.addWidget(self.piece_combo)
        
        left_column.addLayout(piece_selection_layout)

        # Cost input (if enabled)
        if self.allow_costs:
            cost_layout = QHBoxLayout()
            cost_layout.addWidget(QLabel("Cost:"))
            
            self.cost_spin = QSpinBox()
            self.cost_spin.setRange(0, 999)
            self.cost_spin.setValue(1)
            self.cost_spin.setMinimumWidth(80)
            cost_layout.addWidget(self.cost_spin)
            
            left_column.addLayout(cost_layout)

        # Add button
        self.add_button = QPushButton("Add Piece")
        self.add_button.clicked.connect(self.add_piece)
        left_column.addWidget(self.add_button)

        # Add stretch to push everything to top
        left_column.addStretch()

        # RIGHT COLUMN: Selected pieces display
        right_column = QVBoxLayout()
        right_column.setSpacing(8)

        # Selected pieces label
        selected_label = QLabel("Selected:")
        selected_font = QFont()
        selected_font.setBold(True)
        selected_label.setFont(selected_font)
        right_column.addWidget(selected_label)

        # Scroll area for selected pieces
        self.pieces_scroll = QScrollArea()
        self.pieces_scroll.setWidgetResizable(True)
        self.pieces_scroll.setMinimumHeight(120)
        self.pieces_scroll.setMaximumHeight(200)
        self.pieces_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.pieces_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Container for piece items
        self.pieces_container = QWidget()
        self.pieces_layout = QVBoxLayout(self.pieces_container)
        self.pieces_layout.setContentsMargins(5, 5, 5, 5)
        self.pieces_layout.setSpacing(3)
        self.pieces_scroll.setWidget(self.pieces_container)

        right_column.addWidget(self.pieces_scroll)

        # Add columns to main content
        main_content.addLayout(left_column, 1)  # Left column takes 1 part
        main_content.addLayout(right_column, 2)  # Right column takes 2 parts

        layout.addLayout(main_content)

        # Update display
        self.update_pieces_display()

    def get_piece_names(self):
        """Get list of available piece names"""
        try:
            from utils.simple_bridge import simple_bridge
            return simple_bridge.list_pieces()
        except:
            return ["Pawn", "Rook", "Knight", "Bishop", "Queen", "King"]

    def add_piece(self):
        """Add selected piece to the list"""
        piece_name = self.piece_combo.currentText()
        if not piece_name:
            return

        cost = self.cost_spin.value() if self.allow_costs else 0
        
        # Check if piece already exists
        for existing_piece in self.pieces:
            if existing_piece['piece'] == piece_name:
                # Update cost if it exists
                existing_piece['cost'] = cost
                self.update_pieces_display()
                self.pieces_changed.emit()
                return

        # Add new piece
        self.pieces.append({'piece': piece_name, 'cost': cost})
        self.update_pieces_display()
        self.pieces_changed.emit()

    def remove_piece(self, piece_name):
        """Remove piece from the list"""
        self.pieces = [p for p in self.pieces if p['piece'] != piece_name]
        self.update_pieces_display()
        self.pieces_changed.emit()

    def update_pieces_display(self):
        """Update the display of selected pieces"""
        # Clear existing items
        while self.pieces_layout.count():
            child = self.pieces_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if not self.pieces:
            # Show "None selected" message
            no_pieces_label = QLabel("None selected")
            no_pieces_label.setStyleSheet("color: #666; font-style: italic;")
            no_pieces_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.pieces_layout.addWidget(no_pieces_label)
        else:
            # Add piece items
            for piece_data in self.pieces:
                piece_item = self.create_piece_item(piece_data)
                self.pieces_layout.addWidget(piece_item)

        # Add stretch to push items to top
        self.pieces_layout.addStretch()

    def create_piece_item(self, piece_data):
        """Create a widget for displaying a single piece"""
        item_frame = QFrame()
        item_frame.setFrameStyle(QFrame.Shape.Box)
        item_frame.setLineWidth(1)
        item_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 2px;
            }
        """)

        layout = QHBoxLayout(item_frame)
        layout.setContentsMargins(6, 4, 6, 4)
        layout.setSpacing(8)

        # Piece name and cost
        if self.allow_costs:
            text = f"{piece_data['piece']} (Cost: {piece_data['cost']})"
        else:
            text = piece_data['piece']
        
        piece_label = QLabel(text)
        piece_label.setFont(QFont("Arial", 9))
        layout.addWidget(piece_label)

        layout.addStretch()

        # Remove button
        remove_btn = QPushButton("×")
        remove_btn.setFixedSize(20, 20)
        remove_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff6b6b;
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #ff5252;
            }
        """)
        remove_btn.clicked.connect(lambda: self.remove_piece(piece_data['piece']))
        layout.addWidget(remove_btn)

        return item_frame

    def get_pieces(self):
        """Get the current list of pieces"""
        return self.pieces.copy()

    def get_selected_pieces(self):
        """Get the current list of selected pieces (alias for get_pieces)"""
        return self.get_pieces()

    def set_pieces(self, pieces):
        """Set the pieces list"""
        self.pieces = pieces.copy() if pieces else []
        self.update_pieces_display()

    def clear_pieces(self):
        """Clear all pieces"""
        self.pieces = []
        self.update_pieces_display()
        self.pieces_changed.emit()

    def get_piece_names_only(self):
        """Get just the piece names as a list"""
        return [piece['piece'] for piece in self.pieces]

    def set_pieces_from_names(self, piece_names, default_cost=1):
        """Set pieces from a list of names with default cost"""
        self.pieces = []
        for name in piece_names:
            self.pieces.append({'piece': name, 'cost': default_cost})
        self.update_pieces_display()

    def get_total_cost(self):
        """Get total cost of all pieces"""
        return sum(piece['cost'] for piece in self.pieces)

    def has_piece(self, piece_name):
        """Check if a specific piece is selected"""
        return any(piece['piece'] == piece_name for piece in self.pieces)

    def get_piece_cost(self, piece_name):
        """Get cost of a specific piece"""
        for piece in self.pieces:
            if piece['piece'] == piece_name:
                return piece['cost']
        return 0

    def set_piece_cost(self, piece_name, cost):
        """Set cost of a specific piece"""
        for piece in self.pieces:
            if piece['piece'] == piece_name:
                piece['cost'] = cost
                self.update_pieces_display()
                self.pieces_changed.emit()
                return True
        return False

    def enable_costs(self, enabled=True):
        """Enable or disable cost functionality"""
        self.allow_costs = enabled
        if hasattr(self, 'cost_spin'):
            self.cost_spin.setVisible(enabled)
        self.update_pieces_display()

    def set_title(self, title):
        """Set the title of the selector"""
        self.title = title
        # Update title label if it exists
        if hasattr(self, 'title_label'):
            self.title_label.setText(title + ":")

    def is_empty(self):
        """Check if no pieces are selected"""
        return len(self.pieces) == 0

    def count(self):
        """Get number of selected pieces"""
        return len(self.pieces)


class InlineAbilitySelector(QWidget):
    """
    Inline ability selector widget that displays selected abilities in a grid layout
    with add/remove functionality without opening separate dialogs
    """

    abilities_changed = pyqtSignal()  # Emitted when abilities list changes

    def __init__(self, parent=None, title="Abilities", allow_costs=True):
        super().__init__(parent)
        self.title = title
        self.allow_costs = allow_costs
        self.abilities = []  # List of {'ability': str, 'cost': int} dicts
        self.selected_index = -1  # Track selected ability index
        self.setup_ui()

    def setup_ui(self):
        """Set up the enhanced two-column inline ability selector UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # Header with title
        title_label = QLabel(self.title + ":")
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # Main content area with two columns
        main_content = QHBoxLayout()
        main_content.setSpacing(12)

        # LEFT COLUMN: Options and Controls
        left_column = QVBoxLayout()
        left_column.setSpacing(8)

        # Ability selection dropdown
        ability_selection_layout = QHBoxLayout()
        ability_selection_layout.addWidget(QLabel("Ability:"))

        self.ability_combo = QComboBox()
        self.ability_combo.setMinimumWidth(120)
        self.ability_combo.addItems(self.get_ability_names())
        self.ability_combo.currentTextChanged.connect(self.update_ability_preview)
        ability_selection_layout.addWidget(self.ability_combo)

        left_column.addLayout(ability_selection_layout)

        # Ability preview
        self.ability_preview = QLabel("Select an ability to see preview")
        self.ability_preview.setWordWrap(True)
        self.ability_preview.setStyleSheet("QLabel { background-color: #2b2b2b; border: 1px solid #555; padding: 8px; border-radius: 4px; }")
        self.ability_preview.setMinimumHeight(60)
        left_column.addWidget(self.ability_preview)

        # Cost input removed per user request

        # Add button
        self.add_button = QPushButton("Add Ability")
        self.add_button.clicked.connect(self.add_ability)
        left_column.addWidget(self.add_button)

        # Add stretch to push everything to top
        left_column.addStretch()

        # RIGHT COLUMN: Selected abilities display
        right_column = QVBoxLayout()
        right_column.setSpacing(8)

        # Selected abilities label
        selected_label = QLabel("Selected:")
        selected_font = QFont()
        selected_font.setBold(True)
        selected_label.setFont(selected_font)
        right_column.addWidget(selected_label)

        # Scroll area for selected abilities
        self.abilities_scroll = QScrollArea()
        self.abilities_scroll.setWidgetResizable(True)
        self.abilities_scroll.setMinimumHeight(120)
        self.abilities_scroll.setMaximumHeight(200)
        self.abilities_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.abilities_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Container for ability items
        self.abilities_container = QWidget()
        self.content_layout = QVBoxLayout(self.abilities_container)
        self.content_layout.setContentsMargins(5, 5, 5, 5)
        self.content_layout.setSpacing(3)
        self.abilities_scroll.setWidget(self.abilities_container)

        right_column.addWidget(self.abilities_scroll)

        # Add columns to main content
        main_content.addLayout(left_column, 1)  # Left column takes 1 part
        main_content.addLayout(right_column, 2)  # Right column takes 2 parts

        layout.addLayout(main_content)

        # Update display
        self.update_abilities_display()
        self.update_ability_preview()

    def get_ability_names(self):
        """Get list of available ability names"""
        try:
            from utils.simple_bridge import simple_bridge
            return simple_bridge.list_abilities()
        except:
            return ["Move", "Capture", "Special", "Passive"]

    def add_ability(self):
        """Add selected ability to the list"""
        ability_name = self.ability_combo.currentText()
        if not ability_name:
            return

        cost = 0  # Cost functionality removed per user request

        # Check if ability already exists
        for existing_ability in self.abilities:
            if existing_ability['ability'] == ability_name:
                # Update cost if it exists
                existing_ability['cost'] = cost
                self.update_abilities_display()
                self.abilities_changed.emit()
                return

        # Add new ability
        self.abilities.append({'ability': ability_name, 'cost': cost})
        self.update_abilities_display()
        self.abilities_changed.emit()

    def remove_ability(self, ability_name):
        """Remove ability from the list"""
        self.abilities = [a for a in self.abilities if a['ability'] != ability_name]
        self.update_abilities_display()
        self.abilities_changed.emit()

    def update_abilities_display(self):
        """Update the display of selected abilities"""
        # Clear existing items
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if not self.abilities:
            # Show "None selected" message
            no_abilities_label = QLabel("None selected")
            no_abilities_label.setStyleSheet("color: #666; font-style: italic;")
            no_abilities_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.content_layout.addWidget(no_abilities_label)
        else:
            # Add ability items
            for ability_data in self.abilities:
                ability_item = self.create_ability_item(ability_data)
                self.content_layout.addWidget(ability_item)

        # Add stretch to push items to top
        self.content_layout.addStretch()

    def create_ability_item(self, ability_data):
        """Create a widget for displaying a single ability"""
        item_frame = QFrame()
        item_frame.setFrameStyle(QFrame.Shape.Box)
        item_frame.setLineWidth(1)
        item_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 2px;
            }
        """)

        layout = QHBoxLayout(item_frame)
        layout.setContentsMargins(6, 4, 6, 4)
        layout.setSpacing(8)

        # Ability name and cost
        if self.allow_costs:
            text = f"{ability_data['ability']} (Cost: {ability_data['cost']})"
        else:
            text = ability_data['ability']

        ability_label = QLabel(text)
        ability_label.setFont(QFont("Arial", 9))
        layout.addWidget(ability_label)

        layout.addStretch()

        # Remove button
        remove_btn = QPushButton("×")
        remove_btn.setFixedSize(20, 20)
        remove_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff6b6b;
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #ff5252;
            }
        """)
        remove_btn.clicked.connect(lambda: self.remove_ability(ability_data['ability']))
        layout.addWidget(remove_btn)

        return item_frame

    def get_abilities(self):
        """Get the current list of abilities"""
        return self.abilities.copy()

    def set_abilities(self, abilities):
        """Set the abilities list"""
        self.abilities = abilities.copy() if abilities else []
        self.update_abilities_display()

    def clear_abilities(self):
        """Clear all abilities"""
        self.abilities = []
        self.update_abilities_display()
        self.abilities_changed.emit()

    def get_ability_names_only(self):
        """Get just the ability names as a list"""
        return [ability['ability'] for ability in self.abilities]

    def set_abilities_from_names(self, ability_names, default_cost=1):
        """Set abilities from a list of names with default cost"""
        self.abilities = []
        for name in ability_names:
            self.abilities.append({'ability': name, 'cost': default_cost})
        self.update_abilities_display()

    def get_total_cost(self):
        """Get total cost of all abilities"""
        return sum(ability['cost'] for ability in self.abilities)

    def has_ability(self, ability_name):
        """Check if a specific ability is selected"""
        return any(ability['ability'] == ability_name for ability in self.abilities)

    def get_ability_cost(self, ability_name):
        """Get cost of a specific ability"""
        for ability in self.abilities:
            if ability['ability'] == ability_name:
                return ability['cost']
        return 0

    def set_ability_cost(self, ability_name, cost):
        """Set cost of a specific ability"""
        for ability in self.abilities:
            if ability['ability'] == ability_name:
                ability['cost'] = cost
                self.update_abilities_display()
                self.abilities_changed.emit()
                return True
        return False

    def enable_costs(self, enabled=True):
        """Enable or disable cost functionality - cost spinner removed per user request"""
        self.allow_costs = enabled
        # Cost spinner functionality removed
        self.update_abilities_display()

    def set_title(self, title):
        """Set the title of the selector"""
        self.title = title
        # Update title label if it exists
        if hasattr(self, 'title_label'):
            self.title_label.setText(title + ":")

    def is_empty(self):
        """Check if no abilities are selected"""
        return len(self.abilities) == 0

    def count(self):
        """Get number of selected abilities"""
        return len(self.abilities)

    def get_ability_description(self, ability_name):
        """Get description of an ability"""
        try:
            from utils.simple_bridge import simple_bridge
            return simple_bridge.get_ability_description(ability_name)
        except:
            return f"Description for {ability_name}"

    def refresh_ability_list(self):
        """Refresh the list of available abilities"""
        current_text = self.ability_combo.currentText()
        self.ability_combo.clear()
        self.ability_combo.addItems(self.get_ability_names())

        # Try to restore previous selection
        index = self.ability_combo.findText(current_text)
        if index >= 0:
            self.ability_combo.setCurrentIndex(index)

    def set_abilities(self, abilities_list):
        """Set the abilities list"""
        self.abilities = abilities_list.copy() if abilities_list else []
        self.selected_index = -1  # Reset selection when setting new abilities
        self.refresh_display()

    def get_abilities(self):
        """Get the current abilities list"""
        return self.abilities.copy()

    def get_ability_names(self):
        """Get list of available ability names"""
        try:
            from utils.simple_bridge import simple_bridge
            return simple_bridge.list_abilities()
        except:
            return ["Heal", "Shield", "Teleport", "Speed Boost", "Fireball", "Ice Blast"]

    def update_ability_preview(self):
        """Update the ability preview with compact format: (Target Type, Ability Name, Cost: X)"""
        ability_name = self.ability_combo.currentText()
        if not ability_name:
            self.ability_preview.setText("Select an ability to see preview")
            return

        try:
            from utils.simple_bridge import simple_bridge
            ability_data, error = simple_bridge.load_ability_for_ui(ability_name)

            if error or not ability_data:
                self.ability_preview.setText(f"Error loading ability: {error or 'Unknown error'}")
                return

            # Extract ability details
            name = ability_data.get('name', ability_name)
            cost = ability_data.get('cost', 0)
            description = ability_data.get('description', 'No description available')

            # Determine target type using same logic as display widget
            target_type = self._determine_target_type_for_preview(ability_data)

            # Create compact preview text in requested format
            preview_text = f"<b>({target_type}, {name}, Cost: {cost})</b><br/><br/>"

            # Add description with hover tooltip for full text
            if len(description) > 80:
                truncated_desc = description[:77] + "..."
                preview_text += f"<i>{truncated_desc}</i>"
                self.ability_preview.setToolTip(f"Full description:\n{description}")
            else:
                preview_text += f"<i>{description}</i>"
                self.ability_preview.setToolTip("")

            self.ability_preview.setText(preview_text)

        except Exception as e:
            self.ability_preview.setText(f"Error loading ability details: {str(e)}")

    def _determine_target_type_for_preview(self, ability_data):
        """Determine the target type from ability data for preview (same logic as display widget)"""
        # Check various target-related fields to determine target type
        if 'capture_target' in ability_data:
            return ability_data['capture_target'].title()
        elif 'convert_target_type' in ability_data:
            return ability_data['convert_target_type'].title()
        elif 'buff_target_list' in ability_data and ability_data['buff_target_list']:
            # Check if targeting specific pieces
            first_target = ability_data['buff_target_list'][0]
            if isinstance(first_target, dict) and 'piece' in first_target:
                piece_name = first_target['piece']
                if 'enemy' in piece_name.lower():
                    return "Enemy"
                elif 'friendly' in piece_name.lower() or 'ally' in piece_name.lower():
                    return "Friendly"
            return "Any"
        elif 'debuff_target_list' in ability_data and ability_data['debuff_target_list']:
            # Similar logic for debuff targets
            first_target = ability_data['debuff_target_list'][0]
            if isinstance(first_target, dict) and 'piece' in first_target:
                piece_name = first_target['piece']
                if 'enemy' in piece_name.lower():
                    return "Enemy"
                elif 'friendly' in piece_name.lower() or 'ally' in piece_name.lower():
                    return "Friendly"
            return "Any"
        elif 'tags' in ability_data:
            # For abilities that primarily target enemies (like capture)
            if 'capture' in ability_data['tags']:
                return "Enemy"
            elif 'buffPiece' in ability_data['tags']:
                return "Friendly"
            elif 'debuffPiece' in ability_data['tags']:
                return "Enemy"

        # Default fallback
        return "Any"

    def add_ability(self):
        """Add a new ability using the left column controls"""
        ability_name = self.ability_combo.currentText()
        if not ability_name:
            return

        # Load ability data to get the actual cost
        try:
            from utils.simple_bridge import simple_bridge
            ability_data, error = simple_bridge.load_ability_for_ui(ability_name)

            if error or not ability_data:
                # Fallback to basic data
                ability_data = {'ability': ability_name, 'cost': 0}
            else:
                # Use the actual cost from the ability file
                cost = ability_data.get('cost', 0)
                ability_data = {'ability': ability_name, 'cost': cost}
        except:
            # Fallback to basic data
            ability_data = {'ability': ability_name, 'cost': 0}

        self.abilities.append(ability_data)
        self.selected_index = -1  # Reset selection when adding new ability
        self.refresh_display()
        self.abilities_changed.emit()

        # Reset controls
        self.ability_combo.setCurrentIndex(0)
        self.update_ability_preview()

    # Edit functionality removed - users can just add new items and remove old ones

    def remove_selected(self):
        """Remove the selected ability"""
        if self.selected_index >= 0 and self.selected_index < len(self.abilities):
            self.abilities.pop(self.selected_index)
            self.selected_index = -1
            self.refresh_display()
            self.abilities_changed.emit()
            self.remove_btn.setEnabled(False)

    def on_ability_selected(self, index):
        """Handle ability selection"""
        self.selected_index = index
        self.remove_btn.setEnabled(True)  # Only enable remove button

        # Update visual selection
        self.refresh_display()

    def refresh_display(self):
        """Refresh the display of selected abilities with selection support"""
        # Clear existing widgets
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if not self.abilities:
            # Show empty state - with light text
            empty_label = QLabel("No abilities selected")
            empty_label.setStyleSheet("""
                font-style: italic;
                padding: 15px;
                border: 2px dashed rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                background: rgba(255, 255, 255, 0.05);
                color: #cccccc;
            """)
            empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.content_layout.addWidget(empty_label)
        else:
            # Show abilities with selection support
            for i, ability_data in enumerate(self.abilities):
                ability_widget = InlineAbilityDisplayWidget(ability_data, i, self)
                ability_widget.ability_selected.connect(self.on_ability_selected)

                # Highlight selected ability
                if i == self.selected_index:
                    ability_widget.set_selected(True)
                else:
                    ability_widget.set_selected(False)

                self.content_layout.addWidget(ability_widget)

        self.content_layout.addStretch()


class InlineAbilityDisplayWidget(QWidget):
    """Enhanced widget to display a single ability with selection support"""

    ability_selected = pyqtSignal(int)  # Emitted with ability index when clicked

    def __init__(self, ability_data, index, parent=None):
        super().__init__(parent)
        self.ability_data = ability_data
        self.index = index
        self.is_selected = False
        self.setup_ui()

    def setup_ui(self):
        """Set up the compact ability display UI in format: (Target Type, Ability Name, Cost: X)"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(6, 4, 6, 4)
        layout.setSpacing(2)

        ability_name = self.ability_data.get('ability', 'Unknown')
        cost = self.ability_data.get('cost', 0)

        # Load full ability data to get display name and target information
        try:
            from utils.simple_bridge import simple_bridge
            full_ability_data, error = simple_bridge.load_ability_for_ui(ability_name)

            if not error and full_ability_data:
                display_name = full_ability_data.get('name', ability_name)
                actual_cost = full_ability_data.get('cost', cost)
                target_type = self._determine_target_type(full_ability_data)
            else:
                display_name = ability_name
                actual_cost = cost
                target_type = "Any"
        except:
            display_name = ability_name
            actual_cost = cost
            target_type = "Any"

        # Create compact display in requested format: (Target Type, Ability Name, Cost: X)
        compact_text = f"({target_type}, {display_name}, Cost: {actual_cost})"

        main_label = QLabel(compact_text)
        main_label.setStyleSheet("""
            font-size: 12px;
            font-weight: bold;
            color: #ffffff;
            padding: 4px 8px;
        """)
        main_label.setWordWrap(True)
        layout.addWidget(main_label)

        # Small index indicator
        index_label = QLabel(f"#{self.index + 1}")
        index_label.setStyleSheet("""
            font-size: 9px;
            padding: 1px 4px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 2px;
            color: #aaaaaa;
            align-self: flex-end;
        """)
        index_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout.addWidget(index_label)

        # Set initial style
        self.update_style()

        # Make clickable
        self.setCursor(Qt.CursorShape.PointingHandCursor)

    def _determine_target_type(self, ability_data):
        """Determine the target type from ability data"""
        # Check various target-related fields to determine target type
        if 'capture_target' in ability_data:
            return ability_data['capture_target'].title()
        elif 'convert_target_type' in ability_data:
            return ability_data['convert_target_type'].title()
        elif 'buff_target_list' in ability_data and ability_data['buff_target_list']:
            # Check if targeting specific pieces
            first_target = ability_data['buff_target_list'][0]
            if isinstance(first_target, dict) and 'piece' in first_target:
                piece_name = first_target['piece']
                if 'enemy' in piece_name.lower():
                    return "Enemy"
                elif 'friendly' in piece_name.lower() or 'ally' in piece_name.lower():
                    return "Friendly"
            return "Any"
        elif 'debuff_target_list' in ability_data and ability_data['debuff_target_list']:
            # Similar logic for debuff targets
            first_target = ability_data['debuff_target_list'][0]
            if isinstance(first_target, dict) and 'piece' in first_target:
                piece_name = first_target['piece']
                if 'enemy' in piece_name.lower():
                    return "Enemy"
                elif 'friendly' in piece_name.lower() or 'ally' in piece_name.lower():
                    return "Friendly"
            return "Any"
        elif 'tags' in ability_data:
            # For abilities that primarily target enemies (like capture)
            if 'capture' in ability_data['tags']:
                return "Enemy"
            elif 'buffPiece' in ability_data['tags']:
                return "Friendly"
            elif 'debuffPiece' in ability_data['tags']:
                return "Enemy"

        # Default fallback
        return "Any"

    def mousePressEvent(self, a0):
        """Handle mouse click for selection"""
        if a0.button() == Qt.MouseButton.LeftButton:
            self.ability_selected.emit(self.index)
        super().mousePressEvent(a0)

    def set_selected(self, selected):
        """Set the selection state"""
        self.is_selected = selected
        self.update_style()

    def update_style(self):
        """Update the widget style based on selection state - compact layout"""
        if self.is_selected:
            self.setStyleSheet("""
                InlineAbilityDisplayWidget {
                    border: 2px solid #28a745;
                    border-radius: 4px;
                    background: rgba(40, 167, 69, 0.15);
                    margin: 1px;
                    min-height: 35px;
                    max-height: 40px;
                }
                InlineAbilityDisplayWidget:hover {
                    border-color: #1e7e34;
                    background: rgba(40, 167, 69, 0.25);
                }
            """)
        else:
            self.setStyleSheet("""
                InlineAbilityDisplayWidget {
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 4px;
                    background: rgba(255, 255, 255, 0.05);
                    margin: 1px;
                    min-height: 35px;
                    max-height: 40px;
                }
                InlineAbilityDisplayWidget:hover {
                    border-color: #28a745;
                    background: rgba(40, 167, 69, 0.1);
                }
            """)


class InlineAbilityAddWidget(QWidget):
    """Widget for adding a new ability inline"""

    ability_added = pyqtSignal(dict)  # Emitted with ability data
    cancelled = pyqtSignal()  # Emitted when cancelled

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """Set up the add ability UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(8)

        # Ability selection
        self.ability_combo = QComboBox()
        self.ability_combo.addItems(self.get_ability_names())
        self.ability_combo.setMinimumWidth(120)
        layout.addWidget(QLabel("Ability:"))
        layout.addWidget(self.ability_combo)

        # Ability preview
        preview_label = QLabel("Preview:")
        preview_label.setStyleSheet("font-size: 10px; color: #666;")
        layout.addWidget(preview_label)

        self.preview_text = QLabel("Select ability")
        self.preview_text.setStyleSheet("font-size: 10px; color: #888; font-style: italic;")
        self.preview_text.setMaximumWidth(100)
        layout.addWidget(self.preview_text)

        # Connect to update preview
        self.ability_combo.currentTextChanged.connect(self.update_preview)

        layout.addStretch()

        # Action buttons
        add_btn = QPushButton("Add")
        add_btn.setMaximumWidth(60)
        add_btn.clicked.connect(self.add_ability)
        layout.addWidget(add_btn)

        cancel_btn = QPushButton("Cancel")
        cancel_btn.setMaximumWidth(60)
        cancel_btn.clicked.connect(self.cancel)
        layout.addWidget(cancel_btn)

        # Style
        self.setStyleSheet("""
            InlineAbilityAddWidget {
                border: 2px dashed #2196F3;
                border-radius: 5px;
                background: #f9f9ff;
            }
        """)

    def get_ability_names(self):
        """Get list of available ability names"""
        try:
            from utils.simple_bridge import simple_bridge
            return simple_bridge.list_abilities()
        except:
            return ["Heal", "Shield", "Teleport", "Speed Boost", "Fireball", "Ice Blast"]

    def update_preview(self):
        """Update the preview text"""
        ability_name = self.ability_combo.currentText()
        if not ability_name:
            self.preview_text.setText("Select ability")
            return

        try:
            from utils.simple_bridge import simple_bridge
            ability_data, error = simple_bridge.load_ability_for_ui(ability_name)

            if not error and ability_data:
                cost = ability_data.get('cost', 0)
                self.preview_text.setText(f"Cost: {cost}")
            else:
                self.preview_text.setText("Cost: 0")
        except:
            self.preview_text.setText("Cost: 0")

    def add_ability(self):
        """Add the ability with cost from ability file"""
        ability_name = self.ability_combo.currentText()
        if not ability_name:
            return

        # Load ability data to get the actual cost
        try:
            from utils.simple_bridge import simple_bridge
            ability_data, error = simple_bridge.load_ability_for_ui(ability_name)

            if error or not ability_data:
                cost = 0
            else:
                cost = ability_data.get('cost', 0)
        except:
            cost = 0

        ability_data = {'ability': ability_name, 'cost': cost}
        self.ability_added.emit(ability_data)

    def cancel(self):
        """Cancel adding"""
        self.cancelled.emit()

    def focus_ability_combo(self):
        """Focus the ability combo box"""
        self.ability_combo.setFocus()
