#!/usr/bin/env python3
"""
Inline Selection Widgets for Adventure Chess
Enhanced widgets for displaying and managing piece/ability selections directly in the configuration tab
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                            QFrame, QScrollArea, QSizePolicy, QGridLayout, QComboBox,
                            QSpinBox, QCheckBox, QGroupBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPalette
from typing import List, Dict, Any, Optional


class InlinePieceSelector(QWidget):
    """
    Inline piece selector widget that displays selected pieces in a grid layout
    with add/remove functionality without opening separate dialogs
    """
    
    pieces_changed = pyqtSignal()  # Emitted when pieces list changes
    
    def __init__(self, parent=None, title="Pieces", allow_costs=True):
        super().__init__(parent)
        self.title = title
        self.allow_costs = allow_costs
        self.pieces = []  # List of {'piece': str, 'cost': int} dicts
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the enhanced two-column inline piece selector UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # Header with title
        title_label = QLabel(self.title + ":")
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # Main content area with two columns
        main_content = QHBoxLayout()
        main_content.setSpacing(12)

        # LEFT COLUMN: Options and Controls
        left_column = QVBoxLayout()
        left_column.setSpacing(8)

        # Add controls section
        add_group = QGroupBox("Add New Piece")
        add_layout = QVBoxLayout(add_group)

        # Target type selection (mutually exclusive checkboxes)
        target_type_layout = QHBoxLayout()
        target_type_layout.addWidget(QLabel("Target Type:"))

        self.friendly_check = QCheckBox("Friendly")
        self.enemy_check = QCheckBox("Enemy")
        self.any_check = QCheckBox("Any")

        # Make them mutually exclusive
        self.friendly_check.toggled.connect(lambda checked: self.on_target_type_changed('Friendly', checked))
        self.enemy_check.toggled.connect(lambda checked: self.on_target_type_changed('Enemy', checked))
        self.any_check.toggled.connect(lambda checked: self.on_target_type_changed('Any', checked))

        target_type_layout.addWidget(self.friendly_check)
        target_type_layout.addWidget(self.enemy_check)
        target_type_layout.addWidget(self.any_check)
        target_type_layout.addStretch()
        add_layout.addLayout(target_type_layout)

        # Piece selection (specific pieces only)
        piece_layout = QHBoxLayout()
        piece_layout.addWidget(QLabel("Specific Piece:"))
        self.piece_combo = QComboBox()
        self.piece_combo.addItems(self.get_specific_piece_names())
        self.piece_combo.setMinimumWidth(120)
        piece_layout.addWidget(self.piece_combo)
        piece_layout.addStretch()
        add_layout.addLayout(piece_layout)

        if self.allow_costs:
            # Cost controls
            cost_layout = QHBoxLayout()
            cost_layout.addWidget(QLabel("Cost:"))

            self.cost_spin = QSpinBox()
            self.cost_spin.setRange(0, 99)
            self.cost_spin.setValue(0)
            self.cost_spin.setMaximumWidth(60)
            cost_layout.addWidget(self.cost_spin)

            self.no_cost_check = QCheckBox("No Cost")
            self.no_cost_check.toggled.connect(self.on_no_cost_toggled)
            cost_layout.addWidget(self.no_cost_check)
            cost_layout.addStretch()
            add_layout.addLayout(cost_layout)

        # Add button
        self.add_btn = QPushButton("+ Add Piece")
        self.add_btn.clicked.connect(self.add_piece)
        add_layout.addWidget(self.add_btn)

        add_group.setMaximumHeight(150)
        left_column.addWidget(add_group)

        # Remove controls section (Edit button removed, only Remove)
        controls_group = QGroupBox("Manage Selected")
        controls_layout = QVBoxLayout(controls_group)

        self.remove_btn = QPushButton("Remove Selected")
        self.remove_btn.clicked.connect(self.remove_selected)
        self.remove_btn.setEnabled(False)
        controls_layout.addWidget(self.remove_btn)

        controls_group.setMaximumHeight(70)  # Reduced height since only one button
        left_column.addWidget(controls_group)

        left_column.addStretch()

        # RIGHT COLUMN: Current Selections
        right_column = QVBoxLayout()

        selections_label = QLabel("Current Selections:")
        selections_font = QFont()
        selections_font.setBold(True)
        selections_label.setFont(selections_font)
        right_column.addWidget(selections_label)

        # Scrollable area for piece display - REDUCED HEIGHT for better fit
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setMinimumHeight(180)  # REDUCED from 300px
        self.scroll_area.setMaximumHeight(250)  # REDUCED from 400px

        # Content widget for the scroll area
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(6, 6, 6, 6)  # REDUCED margins
        self.content_layout.setSpacing(2)  # REDUCED spacing for smaller items

        self.scroll_area.setWidget(self.content_widget)
        right_column.addWidget(self.scroll_area)

        # Set column proportions: left 40%, right 60%
        main_content.addLayout(left_column, 2)
        main_content.addLayout(right_column, 3)

        layout.addLayout(main_content)

        # Initialize selection tracking
        self.selected_index = -1

        # Initial display
        self.refresh_display()
        
    def get_specific_piece_names(self):
        """Get list of specific piece names (excluding Friendly/Enemy/Any)"""
        try:
            from utils.simple_bridge import simple_bridge
            pieces = simple_bridge.list_pieces()
            # Filter out general categories since they're now checkboxes
            return [p for p in pieces if p not in ["Friendly", "Enemy", "Any"]]
        except:
            return ["Pawn", "Knight", "Bishop", "Rook", "Queen", "King"]

    def on_target_type_changed(self, target_type, checked):
        """Handle target type checkbox changes (mutually exclusive for target types)"""
        if checked:
            # Uncheck other target type checkboxes (still mutually exclusive)
            if target_type != 'Friendly':
                self.friendly_check.setChecked(False)
            if target_type != 'Enemy':
                self.enemy_check.setChecked(False)
            if target_type != 'Any':
                self.any_check.setChecked(False)

    def on_no_cost_toggled(self, checked):
        """Handle no cost checkbox toggle"""
        if self.allow_costs and hasattr(self, 'cost_spin'):
            self.cost_spin.setEnabled(not checked)
            if checked:
                self.cost_spin.setValue(0)

    def add_piece(self):
        """Add a new piece using the left column controls with combined targeting"""
        # Build piece name by combining target type and specific piece
        target_type = None
        if self.friendly_check.isChecked():
            target_type = "Friendly"
        elif self.enemy_check.isChecked():
            target_type = "Enemy"
        elif self.any_check.isChecked():
            target_type = "Any"

        # Get specific piece from dropdown (skip general categories and first item)
        specific_piece = self.piece_combo.currentText()
        if specific_piece in ["Any", "Friendly", "Enemy"] or self.piece_combo.currentIndex() == 0:
            specific_piece = None

        # Determine final piece name based on selections
        if target_type and specific_piece:
            # Combined: "Friendly Pawn", "Enemy Queen", etc.
            piece_name = f"{target_type} {specific_piece}"
        elif target_type:
            # Just target type: "Friendly", "Enemy", "Any"
            piece_name = target_type
        elif specific_piece:
            # Just specific piece: "Pawn", "Queen", etc.
            piece_name = specific_piece
        else:
            # Nothing meaningful selected, default to Any
            piece_name = "Any"

        if self.allow_costs and hasattr(self, 'cost_spin'):
            cost = 0 if self.no_cost_check.isChecked() else self.cost_spin.value()
        else:
            cost = 0

        piece_data = {'piece': piece_name, 'cost': cost}
        self.pieces.append(piece_data)
        self.refresh_display()
        self.pieces_changed.emit()

        # Reset controls
        self.piece_combo.setCurrentIndex(0)
        if self.allow_costs:
            self.cost_spin.setValue(0)
            self.no_cost_check.setChecked(False)
        # Reset target type checkboxes
        self.friendly_check.setChecked(False)
        self.enemy_check.setChecked(False)
        self.any_check.setChecked(False)

    # Edit functionality removed - users can just add new items and remove old ones

    def remove_selected(self):
        """Remove the selected piece"""
        if self.selected_index >= 0 and self.selected_index < len(self.pieces):
            self.pieces.pop(self.selected_index)
            self.selected_index = -1
            self.refresh_display()
            self.pieces_changed.emit()
            self.remove_btn.setEnabled(False)
        
    def on_piece_selected(self, index):
        """Handle piece selection"""
        self.selected_index = index
        self.remove_btn.setEnabled(True)  # Only enable remove button

        # Update visual selection
        self.refresh_display()

    def refresh_display(self):
        """Refresh the display of selected pieces with selection support"""
        # Clear existing widgets
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if not self.pieces:
            # Show empty state - with light text
            empty_label = QLabel("No pieces selected")
            empty_label.setStyleSheet("""
                font-style: italic;
                padding: 15px;
                border: 2px dashed rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                background: rgba(255, 255, 255, 0.05);
                color: #cccccc;
            """)
            empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.content_layout.addWidget(empty_label)
        else:
            # Show pieces with selection support
            for i, piece_data in enumerate(self.pieces):
                piece_widget = InlinePieceDisplayWidget(piece_data, i, self)
                piece_widget.piece_selected.connect(self.on_piece_selected)

                # Highlight selected piece
                if i == self.selected_index:
                    piece_widget.set_selected(True)
                else:
                    piece_widget.set_selected(False)

                self.content_layout.addWidget(piece_widget)

        self.content_layout.addStretch()
        
    def set_pieces(self, pieces_list):
        """Set the pieces list"""
        self.pieces = pieces_list.copy() if pieces_list else []
        self.refresh_display()
        
    def get_pieces(self):
        """Get the current pieces list"""
        return self.pieces.copy()


class InlinePieceDisplayWidget(QWidget):
    """Enhanced widget to display a single piece with selection support"""

    piece_selected = pyqtSignal(int)  # Emitted with piece index when clicked

    def __init__(self, piece_data, index, parent=None):
        super().__init__(parent)
        self.piece_data = piece_data
        self.index = index
        self.is_selected = False
        self.setup_ui()

    def setup_ui(self):
        """Set up the enhanced preview display showing checkbox name, piece name, and cost"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(8)

        # Get piece information
        piece_name = self.piece_data.get('piece', 'Unknown')
        cost = self.piece_data.get('cost', 0)

        # Create comprehensive display with enhanced targeting support
        # Determine display type based on piece name
        if piece_name in ['Friendly', 'Enemy', 'Any']:
            # Pure target type pieces
            if piece_name == 'Friendly':
                icon = "🤝"
                type_color = "#4CAF50"  # Green
                display_type = "Target Type"
            elif piece_name == 'Enemy':
                icon = "⚔️"
                type_color = "#F44336"  # Red
                display_type = "Target Type"
            else:  # Any
                icon = "🌐"
                type_color = "#2196F3"  # Blue
                display_type = "Target Type"
        elif piece_name.startswith(('Friendly ', 'Enemy ', 'Any ')):
            # Combined target type + specific piece (e.g., "Friendly Pawn")
            parts = piece_name.split(' ', 1)
            target_type = parts[0]
            specific_piece = parts[1] if len(parts) > 1 else ""

            if target_type == 'Friendly':
                icon = "🤝🎯"
                type_color = "#4CAF50"  # Green
            elif target_type == 'Enemy':
                icon = "⚔️🎯"
                type_color = "#F44336"  # Red
            else:  # Any
                icon = "🌐🎯"
                type_color = "#2196F3"  # Blue
            display_type = "Combined Targeting"
        else:
            # Specific piece only
            icon = "🎯"
            type_color = "#66aaff"
            display_type = "Specific Piece"

        # Create main layout
        main_layout = QVBoxLayout()

        # Icon and display type
        header_layout = QHBoxLayout()
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 16px; margin-right: 5px;")
        header_layout.addWidget(icon_label)

        type_label = QLabel(display_type)
        type_label.setStyleSheet(f"font-weight: bold; color: {type_color}; font-size: 12px;")
        header_layout.addWidget(type_label)
        header_layout.addStretch()

        # Piece and cost info
        info_label = QLabel(f"Target: {piece_name} | Cost: {cost if cost > 0 else 'Free'}")
        info_label.setStyleSheet("color: #ffffff; font-size: 11px;")

        # Container widget
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(6, 4, 6, 4)
        container_layout.setSpacing(2)
        container_layout.addLayout(header_layout)
        container_layout.addWidget(info_label)

        container.setStyleSheet(f"""
            background: {type_color}20;
            border: 1px solid {type_color}60;
            border-radius: 4px;
        """)

        layout.addWidget(container)

        layout.addStretch()

        # Index label for easy identification
        index_label = QLabel(f"#{self.index + 1}")
        index_label.setStyleSheet("""
            font-size: 11px;
            padding: 3px 6px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
            color: #cccccc;
        """)
        layout.addWidget(index_label)

        # Set initial style
        self.update_style()

        # Make clickable
        self.setCursor(Qt.CursorShape.PointingHandCursor)

    def mousePressEvent(self, a0):
        """Handle mouse click for selection"""
        if a0.button() == Qt.MouseButton.LeftButton:
            self.piece_selected.emit(self.index)
        super().mousePressEvent(a0)

    def set_selected(self, selected):
        """Set the selection state"""
        self.is_selected = selected
        self.update_style()

    def update_style(self):
        """Update the widget style based on selection state - dark theme compatible"""
        if self.is_selected:
            self.setStyleSheet("""
                InlinePieceDisplayWidget {
                    border: 2px solid #007bff;
                    border-radius: 6px;
                    background: rgba(0, 123, 255, 0.15);
                    margin: 1px;
                    min-height: 32px;
                    max-height: 40px;
                }
                InlinePieceDisplayWidget:hover {
                    border-color: #0056b3;
                    background: rgba(0, 123, 255, 0.25);
                }
            """)
        else:
            self.setStyleSheet("""
                InlinePieceDisplayWidget {
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 6px;
                    background: rgba(255, 255, 255, 0.05);
                    margin: 1px;
                    min-height: 32px;
                    max-height: 40px;
                }
                InlinePieceDisplayWidget:hover {
                    border-color: #007bff;
                    background: rgba(0, 123, 255, 0.1);
                }
            """)


class InlinePieceAddWidget(QWidget):
    """Widget for adding a new piece inline"""
    
    piece_added = pyqtSignal(dict)  # Emitted with piece data
    cancelled = pyqtSignal()  # Emitted when cancelled
    
    def __init__(self, parent=None, allow_costs=True):
        super().__init__(parent)
        self.allow_costs = allow_costs
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the add piece UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(8)
        
        # Piece selection
        self.piece_combo = QComboBox()
        self.piece_combo.addItems(self.get_piece_names())
        self.piece_combo.setMinimumWidth(120)
        layout.addWidget(QLabel("Piece:"))
        layout.addWidget(self.piece_combo)
        
        if self.allow_costs:
            # Cost controls
            layout.addWidget(QLabel("Cost:"))
            
            self.cost_spin = QSpinBox()
            self.cost_spin.setRange(0, 99)
            self.cost_spin.setValue(0)
            self.cost_spin.setMaximumWidth(60)
            layout.addWidget(self.cost_spin)
            
            self.no_cost_check = QCheckBox("No Cost")
            self.no_cost_check.toggled.connect(self.on_no_cost_toggled)
            layout.addWidget(self.no_cost_check)
        
        layout.addStretch()
        
        # Action buttons
        add_btn = QPushButton("Add")
        add_btn.setMaximumWidth(60)
        add_btn.clicked.connect(self.add_piece)
        layout.addWidget(add_btn)
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.setMaximumWidth(60)
        cancel_btn.clicked.connect(self.cancel)
        layout.addWidget(cancel_btn)
        
        # Style
        self.setStyleSheet("""
            InlinePieceAddWidget {
                border: 2px dashed #4CAF50;
                border-radius: 5px;
                background: #f9fff9;
            }
        """)
        
    def get_piece_names(self):
        """Get list of available piece names"""
        try:
            from utils.simple_bridge import simple_bridge
            return ["Any", "Friendly", "Enemy"] + simple_bridge.list_pieces()
        except:
            return ["Any", "Friendly", "Enemy", "Pawn", "Knight", "Bishop", "Rook", "Queen", "King"]
    
    def on_no_cost_toggled(self, checked):
        """Handle no cost checkbox toggle"""
        if self.allow_costs:
            self.cost_spin.setEnabled(not checked)
            if checked:
                self.cost_spin.setValue(0)
    
    def add_piece(self):
        """Add the piece"""
        piece_name = self.piece_combo.currentText()
        
        if self.allow_costs:
            cost = 0 if self.no_cost_check.isChecked() else self.cost_spin.value()
        else:
            cost = 0
            
        piece_data = {'piece': piece_name, 'cost': cost}
        self.piece_added.emit(piece_data)
        
    def cancel(self):
        """Cancel adding"""
        self.cancelled.emit()
        
    def focus_piece_combo(self):
        """Focus the piece combo box"""
        self.piece_combo.setFocus()


class InlineAbilitySelector(QWidget):
    """
    Inline ability selector widget similar to piece selector but for abilities
    """
    
    abilities_changed = pyqtSignal()  # Emitted when abilities list changes
    
    def __init__(self, parent=None, title="Abilities"):
        super().__init__(parent)
        self.title = title
        self.abilities = []  # List of {'ability': str, 'cost': int} dicts
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the enhanced two-column inline ability selector UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # Header with title
        title_label = QLabel(self.title + ":")
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # Main content area with two columns
        main_content = QHBoxLayout()
        main_content.setSpacing(12)

        # LEFT COLUMN: Options and Controls
        left_column = QVBoxLayout()
        left_column.setSpacing(8)

        # Add controls section
        add_group = QGroupBox("Add New Ability")
        add_layout = QVBoxLayout(add_group)

        # Ability selection
        ability_layout = QHBoxLayout()
        ability_layout.addWidget(QLabel("Ability:"))
        self.ability_combo = QComboBox()
        self.ability_combo.addItems(self.get_ability_names())
        self.ability_combo.setMinimumWidth(120)
        ability_layout.addWidget(self.ability_combo)
        ability_layout.addStretch()
        add_layout.addLayout(ability_layout)

        # Ability preview section
        preview_layout = QVBoxLayout()
        preview_label = QLabel("Preview:")
        preview_label.setStyleSheet("font-weight: bold; font-size: 11px; color: #666;")
        preview_layout.addWidget(preview_label)

        self.ability_preview = QLabel("Select an ability to see details")
        self.ability_preview.setWordWrap(True)
        self.ability_preview.setStyleSheet("""
            padding: 8px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 11px;
            color: #495057;
            min-height: 60px;
        """)
        self.ability_preview.setAlignment(Qt.AlignmentFlag.AlignTop)
        preview_layout.addWidget(self.ability_preview)

        add_layout.addLayout(preview_layout)

        # Connect ability combo change to update preview
        self.ability_combo.currentTextChanged.connect(self.update_ability_preview)

        # Add button
        self.add_btn = QPushButton("+ Add Ability")
        self.add_btn.clicked.connect(self.add_ability)
        add_layout.addWidget(self.add_btn)

        add_group.setMaximumHeight(150)
        left_column.addWidget(add_group)

        # Remove controls section (Edit button removed, only Remove)
        controls_group = QGroupBox("Manage Selected")
        controls_layout = QVBoxLayout(controls_group)

        self.remove_btn = QPushButton("Remove Selected")
        self.remove_btn.clicked.connect(self.remove_selected)
        self.remove_btn.setEnabled(False)
        controls_layout.addWidget(self.remove_btn)

        controls_group.setMaximumHeight(70)  # Reduced height since only one button
        left_column.addWidget(controls_group)

        left_column.addStretch()

        # RIGHT COLUMN: Current Selections
        right_column = QVBoxLayout()

        selections_label = QLabel("Current Selections:")
        selections_font = QFont()
        selections_font.setBold(True)
        selections_label.setFont(selections_font)
        right_column.addWidget(selections_label)

        # Scrollable area for ability display - REDUCED HEIGHT for better fit
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setMinimumHeight(180)  # REDUCED from 300px
        self.scroll_area.setMaximumHeight(250)  # REDUCED from 400px

        # Content widget for the scroll area
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(6, 6, 6, 6)  # REDUCED margins
        self.content_layout.setSpacing(2)  # REDUCED spacing for smaller items

        self.scroll_area.setWidget(self.content_widget)
        right_column.addWidget(self.scroll_area)

        # Set column proportions: left 40%, right 60%
        main_content.addLayout(left_column, 2)
        main_content.addLayout(right_column, 3)

        layout.addLayout(main_content)

        # Initialize selection tracking
        self.selected_index = -1

        # Initial display
        self.refresh_display()
        
    def get_ability_names(self):
        """Get list of available ability names"""
        try:
            from utils.simple_bridge import simple_bridge
            return simple_bridge.list_abilities()
        except:
            return ["Heal", "Shield", "Teleport", "Speed Boost", "Fireball", "Ice Blast"]

    def update_ability_preview(self):
        """Update the ability preview with compact format: (Target Type, Ability Name, Cost: X)"""
        ability_name = self.ability_combo.currentText()
        if not ability_name:
            self.ability_preview.setText("Select an ability to see preview")
            return

        try:
            from utils.simple_bridge import simple_bridge
            ability_data, error = simple_bridge.load_ability_for_ui(ability_name)

            if error or not ability_data:
                self.ability_preview.setText(f"Error loading ability: {error or 'Unknown error'}")
                return

            # Extract ability details
            name = ability_data.get('name', ability_name)
            cost = ability_data.get('cost', 0)
            description = ability_data.get('description', 'No description available')

            # Determine target type using same logic as display widget
            target_type = self._determine_target_type_for_preview(ability_data)

            # Create compact preview text in requested format
            preview_text = f"<b>({target_type}, {name}, Cost: {cost})</b><br/><br/>"

            # Add description with hover tooltip for full text
            if len(description) > 80:
                truncated_desc = description[:77] + "..."
                preview_text += f"<i>{truncated_desc}</i>"
                self.ability_preview.setToolTip(f"Full description:\n{description}")
            else:
                preview_text += f"<i>{description}</i>"
                self.ability_preview.setToolTip("")

            self.ability_preview.setText(preview_text)

        except Exception as e:
            self.ability_preview.setText(f"Error loading ability details: {str(e)}")

    def _determine_target_type_for_preview(self, ability_data):
        """Determine the target type from ability data for preview (same logic as display widget)"""
        # Check various target-related fields to determine target type
        if 'capture_target' in ability_data:
            return ability_data['capture_target'].title()
        elif 'convert_target_type' in ability_data:
            return ability_data['convert_target_type'].title()
        elif 'buff_target_list' in ability_data and ability_data['buff_target_list']:
            # Check if targeting specific pieces
            first_target = ability_data['buff_target_list'][0]
            if isinstance(first_target, dict) and 'piece' in first_target:
                piece_name = first_target['piece']
                if 'enemy' in piece_name.lower():
                    return "Enemy"
                elif 'friendly' in piece_name.lower() or 'ally' in piece_name.lower():
                    return "Friendly"
            return "Any"
        elif 'debuff_target_list' in ability_data and ability_data['debuff_target_list']:
            # Similar logic for debuff targets
            first_target = ability_data['debuff_target_list'][0]
            if isinstance(first_target, dict) and 'piece' in first_target:
                piece_name = first_target['piece']
                if 'enemy' in piece_name.lower():
                    return "Enemy"
                elif 'friendly' in piece_name.lower() or 'ally' in piece_name.lower():
                    return "Friendly"
            return "Any"
        elif 'tags' in ability_data:
            # For abilities that primarily target enemies (like capture)
            if 'capture' in ability_data['tags']:
                return "Enemy"
            elif 'buffPiece' in ability_data['tags']:
                return "Friendly"
            elif 'debuffPiece' in ability_data['tags']:
                return "Enemy"

        # Default fallback
        return "Any"

    def add_ability(self):
        """Add a new ability using the left column controls"""
        ability_name = self.ability_combo.currentText()
        if not ability_name:
            return

        # Load ability data to get the actual cost
        try:
            from utils.simple_bridge import simple_bridge
            ability_data, error = simple_bridge.load_ability_for_ui(ability_name)

            if error or not ability_data:
                # Fallback to basic data
                ability_data = {'ability': ability_name, 'cost': 0}
            else:
                # Use the actual cost from the ability file
                cost = ability_data.get('cost', 0)
                ability_data = {'ability': ability_name, 'cost': cost}
        except:
            # Fallback to basic data
            ability_data = {'ability': ability_name, 'cost': 0}

        self.abilities.append(ability_data)
        self.refresh_display()
        self.abilities_changed.emit()

        # Reset controls
        self.ability_combo.setCurrentIndex(0)
        self.update_ability_preview()

    # Edit functionality removed - users can just add new items and remove old ones

    def remove_selected(self):
        """Remove the selected ability"""
        if self.selected_index >= 0 and self.selected_index < len(self.abilities):
            self.abilities.pop(self.selected_index)
            self.selected_index = -1
            self.refresh_display()
            self.abilities_changed.emit()
            self.remove_btn.setEnabled(False)

    def on_ability_selected(self, index):
        """Handle ability selection"""
        self.selected_index = index
        self.remove_btn.setEnabled(True)  # Only enable remove button

        # Update visual selection
        self.refresh_display()
            
    def refresh_display(self):
        """Refresh the display of selected abilities with selection support"""
        # Clear existing widgets
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if not self.abilities:
            # Show empty state - with light text
            empty_label = QLabel("No abilities selected")
            empty_label.setStyleSheet("""
                font-style: italic;
                padding: 15px;
                border: 2px dashed rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                background: rgba(255, 255, 255, 0.05);
                color: #cccccc;
            """)
            empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.content_layout.addWidget(empty_label)
        else:
            # Show abilities with selection support
            for i, ability_data in enumerate(self.abilities):
                ability_widget = InlineAbilityDisplayWidget(ability_data, i, self)
                ability_widget.ability_selected.connect(self.on_ability_selected)

                # Highlight selected ability
                if i == self.selected_index:
                    ability_widget.set_selected(True)
                else:
                    ability_widget.set_selected(False)

                self.content_layout.addWidget(ability_widget)

        self.content_layout.addStretch()
        
    def set_abilities(self, abilities_list):
        """Set the abilities list"""
        self.abilities = abilities_list.copy() if abilities_list else []
        self.refresh_display()
        
    def get_abilities(self):
        """Get the current abilities list"""
        return self.abilities.copy()


class InlineAbilityDisplayWidget(QWidget):
    """Enhanced widget to display a single ability with selection support"""

    ability_selected = pyqtSignal(int)  # Emitted with ability index when clicked

    def __init__(self, ability_data, index, parent=None):
        super().__init__(parent)
        self.ability_data = ability_data
        self.index = index
        self.is_selected = False
        self.setup_ui()

    def setup_ui(self):
        """Set up the compact ability display UI in format: (Target Type, Ability Name, Cost: X)"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(6, 4, 6, 4)
        layout.setSpacing(2)

        ability_name = self.ability_data.get('ability', 'Unknown')
        cost = self.ability_data.get('cost', 0)

        # Load full ability data to get display name and target information
        try:
            from utils.simple_bridge import simple_bridge
            full_ability_data, error = simple_bridge.load_ability_for_ui(ability_name)

            if not error and full_ability_data:
                display_name = full_ability_data.get('name', ability_name)
                actual_cost = full_ability_data.get('cost', cost)
                target_type = self._determine_target_type(full_ability_data)
            else:
                display_name = ability_name
                actual_cost = cost
                target_type = "Any"
        except:
            display_name = ability_name
            actual_cost = cost
            target_type = "Any"

        # Create compact display in requested format: (Target Type, Ability Name, Cost: X)
        compact_text = f"({target_type}, {display_name}, Cost: {actual_cost})"

        main_label = QLabel(compact_text)
        main_label.setStyleSheet("""
            font-size: 12px;
            font-weight: bold;
            color: #ffffff;
            padding: 4px 8px;
        """)
        main_label.setWordWrap(True)
        layout.addWidget(main_label)

        # Small index indicator
        index_label = QLabel(f"#{self.index + 1}")
        index_label.setStyleSheet("""
            font-size: 9px;
            padding: 1px 4px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 2px;
            color: #aaaaaa;
        """)
        index_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout.addWidget(index_label)

        # Set initial style
        self.update_style()

        # Make clickable
        self.setCursor(Qt.CursorShape.PointingHandCursor)

    def _determine_target_type(self, ability_data):
        """Determine the target type from ability data"""
        # Check various target-related fields to determine target type
        if 'capture_target' in ability_data:
            return ability_data['capture_target'].title()
        elif 'convert_target_type' in ability_data:
            return ability_data['convert_target_type'].title()
        elif 'buff_target_list' in ability_data and ability_data['buff_target_list']:
            # Check if targeting specific pieces
            first_target = ability_data['buff_target_list'][0]
            if isinstance(first_target, dict) and 'piece' in first_target:
                piece_name = first_target['piece']
                if 'enemy' in piece_name.lower():
                    return "Enemy"
                elif 'friendly' in piece_name.lower() or 'ally' in piece_name.lower():
                    return "Friendly"
            return "Any"
        elif 'debuff_target_list' in ability_data and ability_data['debuff_target_list']:
            # Similar logic for debuff targets
            first_target = ability_data['debuff_target_list'][0]
            if isinstance(first_target, dict) and 'piece' in first_target:
                piece_name = first_target['piece']
                if 'enemy' in piece_name.lower():
                    return "Enemy"
                elif 'friendly' in piece_name.lower() or 'ally' in piece_name.lower():
                    return "Friendly"
            return "Any"
        elif 'tags' in ability_data:
            # For abilities that primarily target enemies (like capture)
            if 'capture' in ability_data['tags']:
                return "Enemy"
            elif 'buffPiece' in ability_data['tags']:
                return "Friendly"
            elif 'debuffPiece' in ability_data['tags']:
                return "Enemy"

        # Default fallback
        return "Any"

    def mousePressEvent(self, a0):
        """Handle mouse click for selection"""
        if a0.button() == Qt.MouseButton.LeftButton:
            self.ability_selected.emit(self.index)
        super().mousePressEvent(a0)

    def set_selected(self, selected):
        """Set the selection state"""
        self.is_selected = selected
        self.update_style()

    def update_style(self):
        """Update the widget style based on selection state - compact layout"""
        if self.is_selected:
            self.setStyleSheet("""
                InlineAbilityDisplayWidget {
                    border: 2px solid #28a745;
                    border-radius: 4px;
                    background: rgba(40, 167, 69, 0.15);
                    margin: 1px;
                    min-height: 35px;
                    max-height: 40px;
                }
                InlineAbilityDisplayWidget:hover {
                    border-color: #1e7e34;
                    background: rgba(40, 167, 69, 0.25);
                }
            """)
        else:
            self.setStyleSheet("""
                InlineAbilityDisplayWidget {
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 4px;
                    background: rgba(255, 255, 255, 0.05);
                    margin: 1px;
                    min-height: 35px;
                    max-height: 40px;
                }
                InlineAbilityDisplayWidget:hover {
                    border-color: #28a745;
                    background: rgba(40, 167, 69, 0.1);
                }
            """)


class InlineAbilityAddWidget(QWidget):
    """Widget for adding a new ability inline"""
    
    ability_added = pyqtSignal(dict)  # Emitted with ability data
    cancelled = pyqtSignal()  # Emitted when cancelled
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the add ability UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(8)
        
        # Ability selection
        self.ability_combo = QComboBox()
        self.ability_combo.addItems(self.get_ability_names())
        self.ability_combo.setMinimumWidth(120)
        layout.addWidget(QLabel("Ability:"))
        layout.addWidget(self.ability_combo)
        
        # Ability preview
        preview_label = QLabel("Preview:")
        preview_label.setStyleSheet("font-size: 10px; color: #666;")
        layout.addWidget(preview_label)

        self.preview_text = QLabel("Select ability")
        self.preview_text.setStyleSheet("font-size: 10px; color: #888; font-style: italic;")
        self.preview_text.setMaximumWidth(100)
        layout.addWidget(self.preview_text)

        # Connect to update preview
        self.ability_combo.currentTextChanged.connect(self.update_preview)
        
        layout.addStretch()
        
        # Action buttons
        add_btn = QPushButton("Add")
        add_btn.setMaximumWidth(60)
        add_btn.clicked.connect(self.add_ability)
        layout.addWidget(add_btn)
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.setMaximumWidth(60)
        cancel_btn.clicked.connect(self.cancel)
        layout.addWidget(cancel_btn)
        
        # Style
        self.setStyleSheet("""
            InlineAbilityAddWidget {
                border: 2px dashed #2196F3;
                border-radius: 5px;
                background: #f9f9ff;
            }
        """)
        
    def get_ability_names(self):
        """Get list of available ability names"""
        try:
            from utils.simple_bridge import simple_bridge
            return simple_bridge.list_abilities()
        except:
            return ["Heal", "Shield", "Teleport", "Speed Boost", "Fireball", "Ice Blast"]
    
    def update_preview(self):
        """Update the preview text"""
        ability_name = self.ability_combo.currentText()
        if not ability_name:
            self.preview_text.setText("Select ability")
            return

        try:
            from utils.simple_bridge import simple_bridge
            ability_data, error = simple_bridge.load_ability_for_ui(ability_name)

            if not error and ability_data:
                cost = ability_data.get('cost', 0)
                self.preview_text.setText(f"Cost: {cost}")
            else:
                self.preview_text.setText("Cost: 0")
        except:
            self.preview_text.setText("Cost: 0")

    def add_ability(self):
        """Add the ability with cost from ability file"""
        ability_name = self.ability_combo.currentText()
        if not ability_name:
            return

        # Load ability data to get the actual cost
        try:
            from utils.simple_bridge import simple_bridge
            ability_data, error = simple_bridge.load_ability_for_ui(ability_name)

            if error or not ability_data:
                cost = 0
            else:
                cost = ability_data.get('cost', 0)
        except:
            cost = 0

        ability_data = {'ability': ability_name, 'cost': cost}
        self.ability_added.emit(ability_data)
        
    def cancel(self):
        """Cancel adding"""
        self.cancelled.emit()
        
    def focus_ability_combo(self):
        """Focus the ability combo box"""
        self.ability_combo.setFocus()
