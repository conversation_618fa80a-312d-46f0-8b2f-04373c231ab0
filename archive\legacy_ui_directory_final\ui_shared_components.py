"""
Shared UI Components for Adventure Chess Creator

This file provides backward compatibility by importing the refactored
UI components from the ui.components package.

The original ui_shared_components.py (462 lines) has been refactored
into a modular package with the following components:
- grid_components.py: Grid-based widgets
- file_operations.py: File operation widgets  
- status_display.py: Status and validation widgets
- ui_utilities.py: Utility functions for UI creation

Original file archived at: archive/ui_components_refactoring/ui_shared_components_original.py
"""

# Import all components from the refactored package
from .components import (
    # Grid components
    GridToggleWidget,
    AreaEffectGridWidget,
    
    # File operations
    FileOperationsWidget,
    
    # Status display
    ValidationStatusWidget,
    
    # Utility functions
    create_section_header,
    create_info_box,
    create_legend_item,
    create_dialog_buttons,
    create_grid_instructions
)

# Re-export all components for backward compatibility
__all__ = [
    # Grid components
    "GridToggleWidget",
    "AreaEffectGridWidget",
    
    # File operations
    "FileOperationsWidget",
    
    # Status display
    "ValidationStatusWidget",
    
    # Utility functions
    "create_section_header",
    "create_info_box",
    "create_legend_item",
    "create_dialog_buttons",
    "create_grid_instructions"
]

# Version info
__version__ = "1.1.0"
__refactored__ = "2025-06-25"
