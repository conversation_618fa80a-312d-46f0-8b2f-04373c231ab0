"""
Core Workflow Module for Adventure Chess Creator

This module contains workflow optimization and enhancement systems.
Consolidated from the enhancements/workflow/ folder for better organization.
"""

from .undo_redo_system import EditorCommand, FieldChangeCommand, DataChangeCommand, UndoRedoManager
from .keyboard_shortcuts import KeyboardShortcutManager
from .auto_save_system import <PERSON>SaveManager, BackupManager
from .template_system import TemplateManager, TemplateDialog
from .workflow_integration import WorkflowIntegrator, integrate_workflow_optimization, add_workflow_menu

__all__ = [
    'EditorCommand',
    'FieldChangeCommand',
    'DataChangeCommand',
    'UndoRedoManager',
    'KeyboardShortcutManager',
    'AutoSaveManager',
    'BackupManager',
    'TemplateManager',
    'TemplateDialog',
    'WorkflowIntegrator',
    'integrate_workflow_optimization',
    'add_workflow_menu'
]
