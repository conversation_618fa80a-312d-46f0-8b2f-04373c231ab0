"""
Core Error Handling Module for Adventure Chess Creator

This module contains comprehensive error handling functionality
moved from enhancements/ui/error_handling_system.py for better organization.
"""

# Import all error handling components
from .error_handling_system import (
    # Core error handling classes
    ErrorSeverity,
    <PERSON>rrorCategory,
    ErrorContext,
    EnhancedErrorHandler,
    
    # Data manager integration
    EnhancedDirectDataManager,
    
    # User-friendly error system
    UserFriendlyError,
    ErrorMessageTranslator,
    UserFriendlyErrorDialog,
    
    # Convenience functions
    safe_file_load,
    safe_file_save,
    show_user_friendly_error,
    
    # Global error handler
    error_handler,
)

# Re-export all components
__all__ = [
    # Core error handling classes
    "ErrorSeverity",
    "ErrorCategory", 
    "ErrorContext",
    "EnhancedErrorHandler",
    
    # Data manager integration
    "EnhancedDirectDataManager",
    
    # User-friendly error system
    "UserFriendlyError",
    "ErrorMessageTranslator",
    "UserFriendlyErrorDialog",
    
    # Convenience functions
    "safe_file_load",
    "safe_file_save",
    "show_user_friendly_error",
    
    # Global error handler
    "error_handler",
]
