"""
Core Error Handling Module for Adventure Chess Creator

This module contains comprehensive error handling functionality
moved from enhancements/ui/error_handling_system.py for better organization.
"""

# Import all error handling components
from .error_handling_system import (
    # Core error handling classes
    Error<PERSON>ever<PERSON>,
    <PERSON>rror<PERSON>ategory,
    <PERSON>rror<PERSON>ontex<PERSON>,
    ErrorHandler,

    # Data manager integration
    DirectDataManager,
    
    # User-friendly error system
    UserFriendlyError,
    ErrorMessageTranslator,
    UserFriendlyErrorDialog,
    
    # Convenience functions
    safe_file_load,
    safe_file_save,
    show_user_friendly_error,
    
    # Global error handler
    error_handler,
)

# Import standardized error decorators
from .standardized_error_decorators import (
    # Core decorators
    standardized_error_handler,
    data_operation_error_handler,
    ui_operation_error_handler,
    file_operation_error_handler,
    validation_error_handler,

    # Convenience decorators
    data_load_handler,
    data_save_handler,
    data_delete_handler,
    ui_handler,
    file_handler,
    validation_handler,
)

# Re-export all components
__all__ = [
    # Core error handling classes
    "ErrorSeverity",
    "ErrorCategory",
    "ErrorContext",
    "ErrorHandler",

    # Data manager integration
    "DirectDataManager",
    
    # User-friendly error system
    "UserFriendlyError",
    "ErrorMessageTranslator",
    "UserFriendlyErrorDialog",
    
    # Convenience functions
    "safe_file_load",
    "safe_file_save",
    "show_user_friendly_error",
    
    # Global error handler
    "error_handler",

    # Standardized error decorators
    "standardized_error_handler",
    "data_operation_error_handler",
    "ui_operation_error_handler",
    "file_operation_error_handler",
    "validation_error_handler",

    # Convenience decorators
    "data_load_handler",
    "data_save_handler",
    "data_delete_handler",
    "ui_handler",
    "file_handler",
    "validation_handler",
]
