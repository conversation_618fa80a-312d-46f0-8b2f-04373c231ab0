"""
Standardized Error Handling Decorators for Adventure Chess Creator

This module provides consistent error handling decorators that ensure all modules
use the existing EnhancedErrorHandler system uniformly across the application.

These decorators eliminate mixed error handling approaches and provide:
- Consistent error context and recovery suggestions
- Centralized error logging and recovery
- Better user experience for error handling
"""

import functools
import logging
from typing import Any, Callable, Optional, Tuple, Union

from .error_handling_system import error_handler, ErrorSeverity, ErrorCategory

logger = logging.getLogger(__name__)


def standardized_error_handler(
    severity: ErrorSeverity = ErrorSeverity.ERROR,
    category: ErrorCategory = ErrorCategory.GENERAL,
    operation_name: Optional[str] = None,
    return_on_error: Any = None,
    log_success: bool = False
):
    """
    Decorator for standardized error handling across all modules.
    
    Args:
        severity: Error severity level
        category: Error category for classification
        operation_name: Custom operation name (uses function name if None)
        return_on_error: Value to return on error (None by default)
        log_success: Whether to log successful operations
        
    Returns:
        Decorated function with standardized error handling
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            operation = operation_name or f"{func.__module__}.{func.__name__}"
            
            try:
                result = func(*args, **kwargs)
                
                if log_success:
                    logger.debug(f"Successfully completed operation: {operation}")
                
                return result
                
            except Exception as e:
                # Use centralized error handler
                error_context = error_handler.handle_error(
                    e,
                    severity=severity,
                    category=category,
                    operation=operation,
                    context={
                        'function': func.__name__,
                        'module': func.__module__,
                        'args_count': len(args),
                        'kwargs_keys': list(kwargs.keys()) if kwargs else []
                    }
                )
                
                # Return specified error value
                return return_on_error
                
        return wrapper
    return decorator


def data_operation_error_handler(
    operation_type: str = "data_operation",
    return_tuple: bool = True
):
    """
    Specialized decorator for data operations that typically return (result, error) tuples.
    
    Args:
        operation_type: Type of data operation (load, save, delete, etc.)
        return_tuple: Whether to return (None, error_message) tuple on error
        
    Returns:
        Decorated function with data operation error handling
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            operation = f"{operation_type}_{func.__name__}"
            
            try:
                return func(*args, **kwargs)
                
            except Exception as e:
                # Determine error category based on operation type
                if 'load' in operation_type.lower():
                    category = ErrorCategory.DATA_LOADING
                elif 'save' in operation_type.lower():
                    category = ErrorCategory.DATA_SAVING
                elif 'delete' in operation_type.lower():
                    category = ErrorCategory.DATA_DELETION
                else:
                    category = ErrorCategory.DATA_PROCESSING
                
                # Use centralized error handler
                error_context = error_handler.handle_error(
                    e,
                    severity=ErrorSeverity.ERROR,
                    category=category,
                    operation=operation,
                    context={
                        'operation_type': operation_type,
                        'function': func.__name__,
                        'module': func.__module__
                    }
                )
                
                # Return appropriate error format
                if return_tuple:
                    return None, str(e)
                else:
                    return None
                    
        return wrapper
    return decorator


def ui_operation_error_handler(
    show_user_dialog: bool = True,
    fallback_action: Optional[Callable] = None
):
    """
    Specialized decorator for UI operations with user-friendly error handling.
    
    Args:
        show_user_dialog: Whether to show user-friendly error dialog
        fallback_action: Optional fallback action to execute on error
        
    Returns:
        Decorated function with UI operation error handling
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            operation = f"ui_{func.__name__}"
            
            try:
                return func(*args, **kwargs)
                
            except Exception as e:
                # Use centralized error handler with UI category
                error_context = error_handler.handle_error(
                    e,
                    severity=ErrorSeverity.ERROR,
                    category=ErrorCategory.UI_ERROR,
                    operation=operation,
                    context={
                        'function': func.__name__,
                        'module': func.__module__,
                        'show_dialog': show_user_dialog
                    }
                )
                
                # Show user-friendly dialog if requested
                if show_user_dialog:
                    from .error_handling_system import show_user_friendly_error
                    show_user_friendly_error(e, operation)
                
                # Execute fallback action if provided
                if fallback_action:
                    try:
                        return fallback_action(*args, **kwargs)
                    except Exception as fallback_error:
                        logger.error(f"Fallback action failed for {operation}: {fallback_error}")
                
                return None
                
        return wrapper
    return decorator


def file_operation_error_handler(
    file_path_arg: Union[str, int] = 0,
    create_backup: bool = False
):
    """
    Specialized decorator for file operations with path-aware error handling.
    
    Args:
        file_path_arg: Argument name (str) or position (int) containing file path
        create_backup: Whether to create backup before operation
        
    Returns:
        Decorated function with file operation error handling
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            operation = f"file_{func.__name__}"
            
            # Extract file path from arguments
            file_path = None
            try:
                if isinstance(file_path_arg, str):
                    file_path = kwargs.get(file_path_arg)
                elif isinstance(file_path_arg, int) and len(args) > file_path_arg:
                    file_path = args[file_path_arg]
            except (IndexError, KeyError):
                pass
            
            try:
                return func(*args, **kwargs)
                
            except Exception as e:
                # Use centralized error handler with file context
                error_context = error_handler.handle_error(
                    e,
                    severity=ErrorSeverity.ERROR,
                    category=ErrorCategory.FILE_SYSTEM,
                    operation=operation,
                    file_path=str(file_path) if file_path else None,
                    context={
                        'function': func.__name__,
                        'module': func.__module__,
                        'file_path': str(file_path) if file_path else 'unknown'
                    }
                )
                
                return None
                
        return wrapper
    return decorator


def validation_error_handler(
    validation_type: str = "general",
    return_validation_result: bool = True
):
    """
    Specialized decorator for validation operations.
    
    Args:
        validation_type: Type of validation being performed
        return_validation_result: Whether to return (is_valid, error_message) tuple
        
    Returns:
        Decorated function with validation error handling
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            operation = f"validation_{validation_type}_{func.__name__}"
            
            try:
                return func(*args, **kwargs)
                
            except Exception as e:
                # Use centralized error handler
                error_context = error_handler.handle_error(
                    e,
                    severity=ErrorSeverity.WARNING,  # Validation errors are typically warnings
                    category=ErrorCategory.VALIDATION,
                    operation=operation,
                    context={
                        'validation_type': validation_type,
                        'function': func.__name__,
                        'module': func.__module__
                    }
                )
                
                # Return appropriate validation result
                if return_validation_result:
                    return False, str(e)
                else:
                    return False
                    
        return wrapper
    return decorator


# ========== CONVENIENCE DECORATORS ==========

# Pre-configured decorators for common use cases
data_load_handler = functools.partial(
    data_operation_error_handler, 
    operation_type="load"
)

data_save_handler = functools.partial(
    data_operation_error_handler, 
    operation_type="save"
)

data_delete_handler = functools.partial(
    data_operation_error_handler, 
    operation_type="delete"
)

ui_handler = functools.partial(
    ui_operation_error_handler,
    show_user_dialog=True
)

file_handler = functools.partial(
    file_operation_error_handler,
    file_path_arg=0
)

validation_handler = functools.partial(
    validation_error_handler,
    validation_type="general"
)
