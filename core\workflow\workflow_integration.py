"""
Workflow Integration for Adventure Chess Creator

This module provides integration utilities for applying workflow enhancements
to existing editors throughout the application.
"""

import logging
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import QMessageBox
from PyQt6.QtGui import QAction
from PyQt6.QtCore import QTimer

from .undo_redo_system import UndoRedoManager
from .keyboard_shortcuts import KeyboardShortcutManager
from .auto_save_system import AutoSaveManager
from .template_system import TemplateManager

logger = logging.getLogger(__name__)


class WorkflowIntegrator:
    """Integrates workflow enhancements into existing editors"""

    def __init__(self, editor):
        self.editor = editor
        
        # Feature flags
        self.undo_redo_enabled = True
        self.shortcuts_enabled = True
        self.auto_save_enabled = True
        self.templates_enabled = True
        
        # Component managers
        self.undo_manager = None
        self.shortcut_manager = None
        self.auto_save_manager = None
        self.template_manager = None
        
        # Setup all workflow features
        self.setup_all_features()

    def setup_all_features(self):
        """Setup all workflow enhancement features"""
        try:
            self.setup_undo_redo()
            self.setup_shortcuts()
            self.setup_auto_save()
            self.setup_templates()
            self.connect_editor_signals()
            
            logger.info(f"Workflow integration complete for {self.editor.__class__.__name__}")
            
        except Exception as e:
            logger.error(f"Error setting up workflow features: {e}")

    def setup_undo_redo(self):
        """Setup undo/redo functionality"""
        try:
            self.undo_manager = UndoRedoManager(self.editor)
            
            # Add undo manager to editor
            self.editor.undo_manager = self.undo_manager
            
            logger.info("Undo/Redo system integrated")
            
        except Exception as e:
            logger.error(f"Error setting up undo/redo: {e}")

    def setup_shortcuts(self):
        """Setup enhanced keyboard shortcuts"""
        try:
            self.shortcut_manager = KeyboardShortcutManager(self.editor)
            
            # Add shortcut manager to editor
            self.editor.shortcut_manager = self.shortcut_manager

            logger.info("Enhanced keyboard shortcuts integrated")

        except Exception as e:
            logger.error(f"Error setting up shortcuts: {e}")

    def setup_auto_save(self):
        """Setup auto-save functionality"""
        try:
            self.auto_save_manager = AutoSaveManager(self.editor, interval_seconds=300)  # 5 minutes

            # Connect auto-save signals
            self.auto_save_manager.auto_saved.connect(self.on_auto_save)

            # Start auto-save if editor has data
            if hasattr(self.editor, 'current_filename') and self.editor.current_filename:
                self.auto_save_manager.start()

            # Add auto-save manager to editor
            self.editor.auto_save_manager = self.auto_save_manager

            logger.info("Auto-save system integrated")

        except Exception as e:
            logger.error(f"Error setting up auto-save: {e}")

    def setup_templates(self):
        """Setup template system"""
        try:
            self.template_manager = TemplateManager(self.editor)

            # Add template manager to editor
            self.editor.template_manager = self.template_manager

            # Add template methods to editor
            self.editor.show_templates = self.template_manager.show_template_dialog

            logger.info("Template system integrated")

        except Exception as e:
            logger.error(f"Error setting up templates: {e}")

    def connect_editor_signals(self):
        """Connect to existing editor signals for workflow tracking"""
        try:
            # Override change tracking to include undo/redo
            if hasattr(self.editor, 'mark_unsaved_changes'):
                original_mark_changes = self.editor.mark_unsaved_changes

                def enhanced_mark_changes():
                    original_mark_changes()
                    # Could add undo tracking here if needed

                self.editor.mark_unsaved_changes = enhanced_mark_changes

            logger.debug("Editor signals connected")

        except Exception as e:
            logger.error(f"Error connecting editor signals: {e}")

    def on_auto_save(self, backup_path: str):
        """Handle auto-save completion"""
        try:
            # Could update status bar if available
            logger.info(f"Auto-saved: {backup_path}")

        except Exception as e:
            logger.error(f"Error handling auto-save: {e}")

    def enable_feature(self, feature: str, enabled: bool = True):
        """Enable or disable specific workflow features"""
        try:
            if feature == "undo_redo":
                self.undo_redo_enabled = enabled
                if not enabled and self.undo_manager:
                    self.undo_manager.undo_stack.clear()

            elif feature == "shortcuts":
                self.shortcuts_enabled = enabled
                # Shortcuts are always active once created

            elif feature == "auto_save":
                self.auto_save_enabled = enabled
                if self.auto_save_manager:
                    if enabled:
                        self.auto_save_manager.start()
                    else:
                        self.auto_save_manager.stop()

            elif feature == "templates":
                self.templates_enabled = enabled
                # Templates are always available once created

            logger.info(f"Feature '{feature}' {'enabled' if enabled else 'disabled'}")

        except Exception as e:
            logger.error(f"Error toggling feature {feature}: {e}")

    def get_workflow_status(self) -> Dict[str, Any]:
        """Get current workflow system status"""
        status = {
            "undo_redo": {
                "enabled": self.undo_redo_enabled,
                "can_undo": self.undo_manager.can_undo() if self.undo_manager else False,
                "can_redo": self.undo_manager.can_redo() if self.undo_manager else False,
                "undo_count": self.undo_manager.undo_stack.index() if self.undo_manager else 0
            },
            "shortcuts": {
                "enabled": self.shortcuts_enabled,
                "count": len(self.shortcut_manager.shortcuts) if self.shortcut_manager else 0
            },
            "auto_save": {
                "enabled": self.auto_save_enabled,
                "running": self.auto_save_manager.timer.isActive() if self.auto_save_manager else False,
                "interval": self.auto_save_manager.interval_seconds if self.auto_save_manager else 0
            },
            "templates": {
                "enabled": self.templates_enabled,
                "piece_count": len(self.template_manager.piece_templates) if self.template_manager else 0,
                "ability_count": len(self.template_manager.ability_templates) if self.template_manager else 0
            }
        }
        return status


def integrate_workflow_optimization(editor):
    """
    Convenience function to integrate workflow optimization into an editor

    Args:
        editor: The editor instance to enhance

    Returns:
        WorkflowIntegrator instance
    """
    try:
        integrator = WorkflowIntegrator(editor)
        logger.info(f"Workflow optimization integrated into {editor.__class__.__name__}")
        return integrator

    except Exception as e:
        logger.error(f"Failed to integrate workflow optimization: {e}")
        return None


def add_workflow_menu(editor, integrator: WorkflowIntegrator):
    """
    Add workflow menu to editor's menu bar

    Args:
        editor: The editor instance
        integrator: The workflow integrator instance
    """
    try:
        if hasattr(editor, 'menuBar'):
            workflow_menu = editor.menuBar().addMenu("Workflow")

            # Templates action
            templates_action = QAction("Show Templates", editor)
            templates_action.setShortcut("Ctrl+T")
            templates_action.triggered.connect(integrator.template_manager.show_template_dialog)
            workflow_menu.addAction(templates_action)

            workflow_menu.addSeparator()

            # Auto-save toggle
            auto_save_action = QAction("Toggle Auto-save", editor)
            auto_save_action.setCheckable(True)
            auto_save_action.setChecked(integrator.auto_save_enabled)
            auto_save_action.triggered.connect(
                lambda checked: integrator.enable_feature("auto_save", checked)
            )
            workflow_menu.addAction(auto_save_action)

            # Workflow status
            status_action = QAction("Show Workflow Status", editor)
            status_action.triggered.connect(lambda: show_workflow_status(editor, integrator))
            workflow_menu.addAction(status_action)

            logger.info("Workflow menu added")

    except Exception as e:
        logger.error(f"Error adding workflow menu: {e}")


def show_workflow_status(editor, integrator: WorkflowIntegrator):
    """Show workflow system status dialog"""
    try:
        status = integrator.get_workflow_status()

        message = "Workflow Optimization Status:\n\n"

        # Undo/Redo status
        undo_status = status["undo_redo"]
        message += f"Undo/Redo: {'Enabled' if undo_status['enabled'] else 'Disabled'}\n"
        message += f"  Can Undo: {undo_status['can_undo']}\n"
        message += f"  Can Redo: {undo_status['can_redo']}\n"
        message += f"  Operations: {undo_status['undo_count']}\n\n"

        # Shortcuts status
        shortcut_status = status["shortcuts"]
        message += f"Shortcuts: {'Enabled' if shortcut_status['enabled'] else 'Disabled'}\n"
        message += f"  Registered: {shortcut_status['count']}\n\n"

        # Auto-save status
        auto_save_status = status["auto_save"]
        message += f"Auto-save: {'Enabled' if auto_save_status['enabled'] else 'Disabled'}\n"
        message += f"  Running: {auto_save_status['running']}\n"
        message += f"  Interval: {auto_save_status['interval']}s\n\n"

        # Templates status
        template_status = status["templates"]
        message += f"Templates: {'Enabled' if template_status['enabled'] else 'Disabled'}\n"
        message += f"  Piece Templates: {template_status['piece_count']}\n"
        message += f"  Ability Templates: {template_status['ability_count']}\n"

        QMessageBox.information(editor, "Workflow Status", message)

    except Exception as e:
        logger.error(f"Error showing workflow status: {e}")
        QMessageBox.warning(editor, "Error", f"Failed to get workflow status: {e}")
