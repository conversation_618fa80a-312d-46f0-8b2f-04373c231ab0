"""
Comprehensive Visual Feedback System for Adventure Chess Creator

This consolidated module provides comprehensive visual feedback improvements including:
- Enhanced loading indicators for all operations
- Real-time validation feedback with live updates
- Improved grid visualization with better color schemes
- Operation status indicators and progress tracking
- Integration with existing UI components
- Automatic feedback application and management

Key Features:
- Enhanced color schemes and visual indicators
- Real-time validation feedback
- Loading progress indicators
- Grid visualization improvements
- Status notifications and alerts
- Integration patches for existing components

Consolidates functionality from:
- visual_feedback_enhancements.py (core visual feedback)
- visual_feedback_integration.py (integration with existing UI)
"""

import logging
from typing import Dict, Any, Optional, Callable, List
from PyQt6.QtCore import QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QObject, QRect
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar, 
    QPushButton, QFrame, QGraphicsOpacityEffect, QApplication,
    QDialog, QTextEdit, QScrollArea, QGroupBox, QGridLayout
)
from PyQt6.QtGui import QColor, QPalette, QFont, QPixmap, QPainter, QBrush
from PyQt6.QtCore import Qt

logger = logging.getLogger(__name__)

# ========== ENHANCED COLOR SCHEMES ==========

class ColorSchemes:
    """Enhanced color schemes for better visual feedback"""
    
    # Status colors
    SUCCESS = "#28a745"
    ERROR = "#dc3545" 
    WARNING = "#ffc107"
    INFO = "#17a2b8"
    LOADING = "#007bff"
    
    # Grid colors - Enhanced for better visibility
    GRID_EMPTY = "#2d3748"
    GRID_MOVE = "#4299e1"      # Blue - movement
    GRID_ATTACK = "#f56565"    # Red - attack
    GRID_BOTH = "#9f7aea"      # Purple - both move and attack
    GRID_ACTION = "#48bb78"    # Green - special action
    GRID_ANY = "#ed8936"       # Orange - any action
    GRID_TARGET = "#ff9800"    # Amber - target position
    GRID_BORDER = "#4a5568"    # Border color
    
    # Validation colors
    VALID = "#d4edda"
    INVALID = "#f8d7da"
    PENDING = "#fff3cd"
    
    # Background colors
    DARK_BG = "#1a202c"
    LIGHT_BG = "#f7fafc"
    CARD_BG = "#ffffff"
    HOVER_BG = "#e2e8f0"

class EnhancedLoadingIndicator(QWidget):
    """Enhanced loading indicator with animations and status"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_loading = False
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Setup the loading indicator UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {ColorSchemes.INFO};
                font-weight: bold;
                font-size: 12px;
                padding: 5px;
            }}
        """)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {ColorSchemes.LOADING};
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background-color: {ColorSchemes.LIGHT_BG};
                height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {ColorSchemes.LOADING};
                border-radius: 6px;
                margin: 1px;
            }}
        """)
        
        # Details label (optional)
        self.details_label = QLabel("")
        self.details_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.details_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 10px;
                padding: 2px;
            }
        """)
        
        layout.addWidget(self.status_label)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.details_label)
        self.setLayout(layout)
        
        # Initially hidden
        self.hide()
    
    def setup_animations(self):
        """Setup fade animations"""
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        
        self.fade_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
    
    def show_loading(self, message: str, details: str = ""):
        """Show loading state with message"""
        self.is_loading = True
        self.status_label.setText(message)
        self.details_label.setText(details)
        self.progress_bar.setValue(0)
        
        # Fade in
        self.show()
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.start()
        
        logger.debug(f"Loading indicator shown: {message}")
    
    def update_progress(self, value: int, message: str = "", details: str = ""):
        """Update progress and optionally message"""
        if self.is_loading:
            self.progress_bar.setValue(value)
            if message:
                self.status_label.setText(message)
            if details:
                self.details_label.setText(details)
    
    def hide_loading(self, success_message: str = ""):
        """Hide loading state with optional success message"""
        if success_message:
            self.status_label.setText(success_message)
            self.status_label.setStyleSheet(f"""
                QLabel {{
                    color: {ColorSchemes.SUCCESS};
                    font-weight: bold;
                    font-size: 12px;
                    padding: 5px;
                }}
            """)
            self.progress_bar.setValue(100)
            
            # Show success briefly then fade out
            QTimer.singleShot(1500, self._fade_out)
        else:
            self._fade_out()
    
    def _fade_out(self):
        """Fade out the loading indicator"""
        self.is_loading = False
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        self.fade_animation.finished.connect(self.hide)
        self.fade_animation.start()

class RealTimeValidationWidget(QWidget):
    """Real-time validation feedback widget"""
    
    validation_changed = pyqtSignal(bool, str)  # is_valid, message
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.validation_rules = []
        self.current_data = {}
        self.setup_ui()
        
        # Validation timer for debouncing
        self.validation_timer = QTimer()
        self.validation_timer.setSingleShot(True)
        self.validation_timer.timeout.connect(self._perform_validation)
    
    def setup_ui(self):
        """Setup validation UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Validation status
        self.status_frame = QFrame()
        self.status_frame.setFrameStyle(QFrame.Shape.Box)
        self.status_frame.setStyleSheet(f"""
            QFrame {{
                border: 1px solid {ColorSchemes.GRID_BORDER};
                border-radius: 6px;
                background-color: {ColorSchemes.CARD_BG};
                padding: 5px;
            }}
        """)
        
        status_layout = QHBoxLayout()
        
        self.status_icon = QLabel("✓")
        self.status_icon.setFixedSize(20, 20)
        self.status_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.status_message = QLabel("Ready for validation")
        self.status_message.setWordWrap(True)
        
        status_layout.addWidget(self.status_icon)
        status_layout.addWidget(self.status_message)
        self.status_frame.setLayout(status_layout)
        
        # Validation details (collapsible)
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout()
        self.details_widget.setLayout(self.details_layout)
        self.details_widget.hide()
        
        # Toggle button for details
        self.toggle_details_btn = QPushButton("Show Details")
        self.toggle_details_btn.clicked.connect(self.toggle_details)
        self.toggle_details_btn.setStyleSheet("""
            QPushButton {
                border: none;
                color: #007bff;
                text-decoration: underline;
                font-size: 11px;
                padding: 2px;
            }
            QPushButton:hover {
                color: #0056b3;
            }
        """)
        
        layout.addWidget(self.status_frame)
        layout.addWidget(self.toggle_details_btn)
        layout.addWidget(self.details_widget)
        self.setLayout(layout)
    
    def add_validation_rule(self, rule_name: str, validator: Callable[[Dict], tuple]):
        """
        Add a validation rule
        
        Args:
            rule_name: Name of the validation rule
            validator: Function that takes data dict and returns (is_valid, message)
        """
        self.validation_rules.append((rule_name, validator))
    
    def update_data(self, data: Dict[str, Any]):
        """Update data and trigger validation"""
        self.current_data = data.copy()
        
        # Debounce validation - wait 500ms after last update
        self.validation_timer.stop()
        self.validation_timer.start(500)
    
    def _perform_validation(self):
        """Perform validation on current data"""
        all_valid = True
        validation_results = []
        
        for rule_name, validator in self.validation_rules:
            try:
                is_valid, message = validator(self.current_data)
                validation_results.append((rule_name, is_valid, message))
                if not is_valid:
                    all_valid = False
            except Exception as e:
                validation_results.append((rule_name, False, f"Validation error: {e}"))
                all_valid = False
        
        self._update_validation_display(all_valid, validation_results)
        self.validation_changed.emit(all_valid, self._get_summary_message(validation_results))
    
    def _update_validation_display(self, all_valid: bool, results: List[tuple]):
        """Update the validation display"""
        if all_valid:
            self.status_icon.setText("✓")
            self.status_icon.setStyleSheet(f"color: {ColorSchemes.SUCCESS}; font-weight: bold;")
            self.status_message.setText("All validations passed")
            self.status_frame.setStyleSheet(f"""
                QFrame {{
                    border: 1px solid {ColorSchemes.SUCCESS};
                    border-radius: 6px;
                    background-color: {ColorSchemes.VALID};
                    padding: 5px;
                }}
            """)
        else:
            self.status_icon.setText("✗")
            self.status_icon.setStyleSheet(f"color: {ColorSchemes.ERROR}; font-weight: bold;")
            
            # Count failed validations
            failed_count = sum(1 for _, is_valid, _ in results if not is_valid)
            self.status_message.setText(f"{failed_count} validation(s) failed")
            self.status_frame.setStyleSheet(f"""
                QFrame {{
                    border: 1px solid {ColorSchemes.ERROR};
                    border-radius: 6px;
                    background-color: {ColorSchemes.INVALID};
                    padding: 5px;
                }}
            """)
        
        # Update details
        self._update_details(results)
    
    def _update_details(self, results: List[tuple]):
        """Update validation details"""
        # Clear existing details
        for i in reversed(range(self.details_layout.count())):
            self.details_layout.itemAt(i).widget().setParent(None)
        
        # Add new details
        for rule_name, is_valid, message in results:
            detail_frame = QFrame()
            detail_layout = QHBoxLayout()
            
            icon = QLabel("✓" if is_valid else "✗")
            icon.setFixedSize(16, 16)
            icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon.setStyleSheet(f"""
                color: {ColorSchemes.SUCCESS if is_valid else ColorSchemes.ERROR};
                font-weight: bold;
                font-size: 12px;
            """)
            
            rule_label = QLabel(f"{rule_name}: {message}")
            rule_label.setWordWrap(True)
            rule_label.setStyleSheet("font-size: 11px; padding: 2px;")
            
            detail_layout.addWidget(icon)
            detail_layout.addWidget(rule_label)
            detail_frame.setLayout(detail_layout)
            
            self.details_layout.addWidget(detail_frame)
    
    def _get_summary_message(self, results: List[tuple]) -> str:
        """Get summary validation message"""
        failed_rules = [rule_name for rule_name, is_valid, _ in results if not is_valid]
        if failed_rules:
            return f"Validation failed: {', '.join(failed_rules)}"
        else:
            return "All validations passed"
    
    def toggle_details(self):
        """Toggle validation details visibility"""
        if self.details_widget.isVisible():
            self.details_widget.hide()
            self.toggle_details_btn.setText("Show Details")
        else:
            self.details_widget.show()
            self.toggle_details_btn.setText("Hide Details")

    def validate_all(self):
        """
        Trigger immediate validation of all rules.

        This method is called by the visual feedback system to perform
        immediate validation without waiting for the debounce timer.
        """
        self.validation_timer.stop()  # Cancel any pending validation
        self._perform_validation()    # Perform validation immediately

class EnhancedGridVisualization(QWidget):
    """Enhanced grid visualization with improved colors and patterns"""
    
    def __init__(self, rows: int = 8, cols: int = 8, parent=None):
        super().__init__(parent)
        self.rows = rows
        self.cols = cols
        self.grid_data = [[0 for _ in range(cols)] for _ in range(rows)]
        self.cell_size = 35
        self.setup_ui()
    
    def setup_ui(self):
        """Setup enhanced grid UI"""
        layout = QVBoxLayout()
        
        # Legend
        legend_frame = QFrame()
        legend_layout = QHBoxLayout()
        
        legend_items = [
            ("Empty", ColorSchemes.GRID_EMPTY),
            ("Move", ColorSchemes.GRID_MOVE),
            ("Attack", ColorSchemes.GRID_ATTACK),
            ("Both", ColorSchemes.GRID_BOTH),
            ("Action", ColorSchemes.GRID_ACTION),
            ("Any", ColorSchemes.GRID_ANY),
            ("Target", ColorSchemes.GRID_TARGET)
        ]
        
        for label, color in legend_items:
            legend_item = self._create_legend_item(label, color)
            legend_layout.addWidget(legend_item)
        
        legend_layout.addStretch()
        legend_frame.setLayout(legend_layout)
        
        # Grid
        self.grid_widget = QWidget()
        self.grid_layout = QGridLayout()
        self.grid_layout.setSpacing(1)
        self.buttons = []
        
        for row in range(self.rows):
            button_row = []
            for col in range(self.cols):
                btn = QPushButton()
                btn.setFixedSize(self.cell_size, self.cell_size)
                btn.setCheckable(True)
                btn.clicked.connect(lambda checked, r=row, c=col: self.cell_clicked(r, c))
                self._update_cell_style(btn, 0)
                self.grid_layout.addWidget(btn, row, col)
                button_row.append(btn)
            self.buttons.append(button_row)
        
        self.grid_widget.setLayout(self.grid_layout)
        
        layout.addWidget(legend_frame)
        layout.addWidget(self.grid_widget)
        self.setLayout(layout)
    
    def _create_legend_item(self, label: str, color: str) -> QWidget:
        """Create a legend item"""
        item = QFrame()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 2, 5, 2)
        
        color_box = QLabel()
        color_box.setFixedSize(16, 16)
        color_box.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border: 1px solid {ColorSchemes.GRID_BORDER};
                border-radius: 2px;
            }}
        """)
        
        text_label = QLabel(label)
        text_label.setStyleSheet("font-size: 11px; color: #495057;")
        
        layout.addWidget(color_box)
        layout.addWidget(text_label)
        item.setLayout(layout)
        
        return item
    
    def _update_cell_style(self, button: QPushButton, value: int):
        """Update cell style based on value"""
        colors = {
            0: ColorSchemes.GRID_EMPTY,
            1: ColorSchemes.GRID_MOVE,
            2: ColorSchemes.GRID_ATTACK,
            3: ColorSchemes.GRID_BOTH,
            4: ColorSchemes.GRID_ACTION,
            5: ColorSchemes.GRID_ANY,
            6: ColorSchemes.GRID_TARGET
        }
        
        color = colors.get(value, ColorSchemes.GRID_EMPTY)
        text_color = "#ffffff" if value > 0 else "#666666"
        
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                border: 1px solid {ColorSchemes.GRID_BORDER};
                border-radius: 3px;
                color: {text_color};
                font-weight: bold;
                font-size: 10px;
            }}
            QPushButton:hover {{
                border: 2px solid #ffffff;
            }}
            QPushButton:pressed {{
                background-color: {ColorSchemes.HOVER_BG};
            }}
        """)
        
        # Add symbols for different types
        symbols = {
            0: "",
            1: "→",
            2: "⚔",
            3: "⚡",
            4: "✦",
            5: "◆",
            6: "🎯"
        }
        button.setText(symbols.get(value, ""))
    
    def cell_clicked(self, row: int, col: int):
        """Handle cell click"""
        # Cycle through values 0-6
        current_value = self.grid_data[row][col]
        new_value = (current_value + 1) % 7
        self.grid_data[row][col] = new_value
        self._update_cell_style(self.buttons[row][col], new_value)
    
    def set_grid_data(self, data: List[List[int]]):
        """Set grid data and update display"""
        self.grid_data = data
        for row in range(self.rows):
            for col in range(self.cols):
                if row < len(data) and col < len(data[row]):
                    value = data[row][col]
                    self.grid_data[row][col] = value
                    self._update_cell_style(self.buttons[row][col], value)
    
    def get_grid_data(self) -> List[List[int]]:
        """Get current grid data"""
        return self.grid_data

# ========== OPERATION FEEDBACK SYSTEM ==========

class OperationFeedbackManager(QObject):
    """Manages feedback for various operations throughout the application"""

    operation_started = pyqtSignal(str, str)  # operation_id, description
    operation_progress = pyqtSignal(str, int, str)  # operation_id, progress, details
    operation_completed = pyqtSignal(str, bool, str)  # operation_id, success, message

    def __init__(self, parent=None):
        super().__init__(parent)
        self.active_operations = {}
        self.feedback_widgets = {}

    def start_operation(self, operation_id: str, description: str, show_progress: bool = True):
        """Start tracking an operation"""
        self.active_operations[operation_id] = {
            'description': description,
            'start_time': QTimer(),
            'show_progress': show_progress
        }

        if show_progress and operation_id in self.feedback_widgets:
            widget = self.feedback_widgets[operation_id]
            widget.show_loading(description)

        self.operation_started.emit(operation_id, description)
        logger.info(f"Operation started: {operation_id} - {description}")

    def update_operation(self, operation_id: str, progress: int, details: str = ""):
        """Update operation progress"""
        if operation_id in self.active_operations:
            if operation_id in self.feedback_widgets:
                widget = self.feedback_widgets[operation_id]
                widget.update_progress(progress, details=details)

            self.operation_progress.emit(operation_id, progress, details)

    def complete_operation(self, operation_id: str, success: bool, message: str = ""):
        """Complete an operation"""
        if operation_id in self.active_operations:
            operation = self.active_operations.pop(operation_id)

            if operation['show_progress'] and operation_id in self.feedback_widgets:
                widget = self.feedback_widgets[operation_id]
                if success:
                    widget.hide_loading(message or "Operation completed successfully")
                else:
                    widget.hide_loading()

            self.operation_completed.emit(operation_id, success, message)

            status = "completed successfully" if success else "failed"
            logger.info(f"Operation {status}: {operation_id} - {message}")

    def register_feedback_widget(self, operation_id: str, widget: EnhancedLoadingIndicator):
        """Register a feedback widget for an operation type"""
        self.feedback_widgets[operation_id] = widget

    def get_active_operations(self) -> List[str]:
        """Get list of active operation IDs"""
        return list(self.active_operations.keys())

class StatusBarEnhancement(QWidget):
    """Enhanced status bar with operation feedback and system status"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.operation_manager = OperationFeedbackManager()
        self.setup_connections()

    def setup_ui(self):
        """Setup enhanced status bar UI"""
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 2, 5, 2)

        # Main status label
        self.main_status = QLabel("Ready")
        self.main_status.setStyleSheet("""
            QLabel {
                color: #495057;
                font-weight: bold;
                padding: 2px 8px;
            }
        """)

        # Operation indicator
        self.operation_indicator = QLabel("")
        self.operation_indicator.setStyleSheet(f"""
            QLabel {{
                color: {ColorSchemes.LOADING};
                font-size: 11px;
                padding: 2px 8px;
            }}
        """)

        # System status indicators
        self.validation_status = QLabel("✓ Valid")
        self.validation_status.setStyleSheet(f"""
            QLabel {{
                color: {ColorSchemes.SUCCESS};
                font-size: 11px;
                padding: 2px 8px;
            }}
        """)

        self.save_status = QLabel("💾 Saved")
        self.save_status.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 11px;
                padding: 2px 8px;
            }
        """)

        # Workflow status (from workflow optimization)
        self.workflow_status = QLabel("⚡ Workflow Ready")
        self.workflow_status.setStyleSheet(f"""
            QLabel {{
                color: {ColorSchemes.INFO};
                font-size: 11px;
                padding: 2px 8px;
            }}
        """)

        layout.addWidget(self.main_status)
        layout.addWidget(QLabel("|"))  # Separator
        layout.addWidget(self.operation_indicator)
        layout.addWidget(QLabel("|"))  # Separator
        layout.addWidget(self.validation_status)
        layout.addWidget(self.save_status)
        layout.addWidget(self.workflow_status)
        layout.addStretch()

        self.setLayout(layout)

    def setup_connections(self):
        """Setup connections to operation manager"""
        self.operation_manager.operation_started.connect(self.on_operation_started)
        self.operation_manager.operation_progress.connect(self.on_operation_progress)
        self.operation_manager.operation_completed.connect(self.on_operation_completed)

    def on_operation_started(self, operation_id: str, description: str):
        """Handle operation start"""
        self.operation_indicator.setText(f"🔄 {description}")
        self.operation_indicator.setStyleSheet(f"""
            QLabel {{
                color: {ColorSchemes.LOADING};
                font-size: 11px;
                padding: 2px 8px;
            }}
        """)

    def on_operation_progress(self, operation_id: str, progress: int, details: str):
        """Handle operation progress"""
        if details:
            self.operation_indicator.setText(f"🔄 {details} ({progress}%)")

    def on_operation_completed(self, operation_id: str, success: bool, message: str):
        """Handle operation completion"""
        if success:
            self.operation_indicator.setText("✅ Operation completed")
            self.operation_indicator.setStyleSheet(f"""
                QLabel {{
                    color: {ColorSchemes.SUCCESS};
                    font-size: 11px;
                    padding: 2px 8px;
                }}
            """)
        else:
            self.operation_indicator.setText(f"❌ {message}")
            self.operation_indicator.setStyleSheet(f"""
                QLabel {{
                    color: {ColorSchemes.ERROR};
                    font-size: 11px;
                    padding: 2px 8px;
                }}
            """)

        # Clear after 3 seconds
        QTimer.singleShot(3000, self.clear_operation_status)

    def clear_operation_status(self):
        """Clear operation status"""
        self.operation_indicator.setText("")

    def update_validation_status(self, is_valid: bool, message: str = ""):
        """Update validation status"""
        if is_valid:
            self.validation_status.setText("✓ Valid")
            self.validation_status.setStyleSheet(f"""
                QLabel {{
                    color: {ColorSchemes.SUCCESS};
                    font-size: 11px;
                    padding: 2px 8px;
                }}
            """)
        else:
            self.validation_status.setText(f"✗ {message}")
            self.validation_status.setStyleSheet(f"""
                QLabel {{
                    color: {ColorSchemes.ERROR};
                    font-size: 11px;
                    padding: 2px 8px;
                }}
            """)

    def update_save_status(self, is_saved: bool, auto_save: bool = False):
        """Update save status"""
        if is_saved:
            icon = "💾" if not auto_save else "🔄"
            text = "Saved" if not auto_save else "Auto-saved"
            self.save_status.setText(f"{icon} {text}")
            self.save_status.setStyleSheet(f"""
                QLabel {{
                    color: {ColorSchemes.SUCCESS};
                    font-size: 11px;
                    padding: 2px 8px;
                }}
            """)
        else:
            self.save_status.setText("📝 Unsaved changes")
            self.save_status.setStyleSheet(f"""
                QLabel {{
                    color: {ColorSchemes.WARNING};
                    font-size: 11px;
                    padding: 2px 8px;
                }}
            """)

    def update_workflow_status(self, undo_count: int, redo_count: int, templates_available: int):
        """Update workflow status"""
        status_parts = []
        if undo_count > 0:
            status_parts.append(f"↶{undo_count}")
        if redo_count > 0:
            status_parts.append(f"↷{redo_count}")
        if templates_available > 0:
            status_parts.append(f"📋{templates_available}")

        if status_parts:
            self.workflow_status.setText(f"⚡ {' '.join(status_parts)}")
        else:
            self.workflow_status.setText("⚡ Workflow Ready")

    @staticmethod
    def enhance_status_bar(status_bar):
        """
        Enhance an existing status bar with visual feedback components.

        Args:
            status_bar: The QStatusBar instance to enhance
        """
        try:
            # Create and add enhanced status bar widget
            enhanced_widget = StatusBarEnhancement()
            status_bar.addPermanentWidget(enhanced_widget)

            logger.debug("Status bar enhanced with visual feedback components")
            return enhanced_widget

        except Exception as e:
            logger.error(f"Error enhancing status bar: {e}")
            return None

# ========== INTEGRATION UTILITIES ==========

class VisualFeedbackIntegrator:
    """Integrates visual feedback enhancements into existing editors"""

    @staticmethod
    def enhance_editor(editor, enable_validation: bool = True, enable_operations: bool = True):
        """
        Enhance an editor with visual feedback components

        Args:
            editor: The editor widget to enhance
            enable_validation: Whether to add real-time validation
            enable_operations: Whether to add operation feedback
        """
        # Add loading indicator
        if not hasattr(editor, 'loading_indicator'):
            editor.loading_indicator = EnhancedLoadingIndicator(editor)
            if hasattr(editor, 'layout') and editor.layout():
                editor.layout().addWidget(editor.loading_indicator)

        # Add validation widget
        if enable_validation and not hasattr(editor, 'validation_widget'):
            editor.validation_widget = RealTimeValidationWidget(editor)
            if hasattr(editor, 'layout') and editor.layout():
                editor.layout().addWidget(editor.validation_widget)

        # Add operation feedback
        if enable_operations and not hasattr(editor, 'operation_manager'):
            editor.operation_manager = OperationFeedbackManager(editor)
            editor.operation_manager.register_feedback_widget('default', editor.loading_indicator)

        # Add enhanced status bar
        if not hasattr(editor, 'enhanced_status_bar'):
            editor.enhanced_status_bar = StatusBarEnhancement(editor)
            if hasattr(editor, 'statusBar') and callable(editor.statusBar):
                status_bar = editor.statusBar()
                status_bar.addPermanentWidget(editor.enhanced_status_bar)

        logger.info(f"Visual feedback enhancements applied to {editor.__class__.__name__}")

    @staticmethod
    def add_validation_rules(editor, rules: List[tuple]):
        """
        Add validation rules to an editor

        Args:
            editor: The editor widget
            rules: List of (rule_name, validator_function) tuples
        """
        if hasattr(editor, 'validation_widget'):
            for rule_name, validator in rules:
                editor.validation_widget.add_validation_rule(rule_name, validator)

    @staticmethod
    def enhance_grid_widget(grid_widget):
        """Enhance an existing grid widget with better visualization"""
        if hasattr(grid_widget, 'buttons'):
            # Apply enhanced styling to existing grid
            for row in grid_widget.buttons:
                for button in row:
                    button.setStyleSheet(f"""
                        QPushButton {{
                            background-color: {ColorSchemes.GRID_EMPTY};
                            border: 1px solid {ColorSchemes.GRID_BORDER};
                            border-radius: 3px;
                            color: #ffffff;
                            font-weight: bold;
                            font-size: 10px;
                        }}
                        QPushButton:checked {{
                            background-color: {ColorSchemes.GRID_MOVE};
                        }}
                        QPushButton:hover {{
                            border: 2px solid #ffffff;
                        }}
                    """)

        logger.info(f"Grid visualization enhanced for {grid_widget.__class__.__name__}")


# ========== ENHANCED GRID VISUALIZER ==========

class EnhancedGridVisualizer:
    """
    Enhanced grid visualizer with static methods for grid enhancement
    This class provides the interface expected by the import statements
    """

    @staticmethod
    def enhance_grid_widget(grid_widget):
        """Enhance an existing grid widget with better visualization"""
        return VisualFeedbackIntegrator.enhance_grid_widget(grid_widget)

    @staticmethod
    def create_enhanced_grid(rows: int = 8, cols: int = 8, parent=None):
        """Create a new enhanced grid visualization widget"""
        return EnhancedGridVisualization(rows, cols, parent)


def create_validation_rules_for_piece_editor():
    """Create validation rules for piece editor"""
    def validate_piece_name(data):
        name = data.get('name', '').strip()
        if not name:
            return False, "Piece name is required"
        if len(name) < 2:
            return False, "Piece name must be at least 2 characters"
        return True, "Piece name is valid"

    def validate_movement_pattern(data):
        pattern = data.get('movement_pattern', [])
        if not pattern or not any(any(row) for row in pattern):
            return False, "Movement pattern cannot be empty"
        return True, "Movement pattern is valid"

    def validate_piece_value(data):
        value = data.get('value', 0)
        if value < 0:
            return False, "Piece value cannot be negative"
        if value > 1000:
            return False, "Piece value seems too high (max 1000)"
        return True, "Piece value is valid"

    return [
        ("Piece Name", validate_piece_name),
        ("Movement Pattern", validate_movement_pattern),
        ("Piece Value", validate_piece_value)
    ]

def create_validation_rules_for_ability_editor():
    """Create validation rules for ability editor"""
    def validate_ability_name(data):
        name = data.get('name', '').strip()
        if not name:
            return False, "Ability name is required"
        if len(name) < 2:
            return False, "Ability name must be at least 2 characters"
        return True, "Ability name is valid"

    def validate_ability_cost(data):
        cost = data.get('cost', 0)
        if cost < 0:
            return False, "Ability cost cannot be negative"
        return True, "Ability cost is valid"

    def validate_ability_tags(data):
        tags = data.get('tags', [])
        if not tags:
            return False, "At least one ability tag is required"
        return True, "Ability tags are valid"

    return [
        ("Ability Name", validate_ability_name),
        ("Ability Cost", validate_ability_cost),
        ("Ability Tags", validate_ability_tags)
    ]


# ========== VISUAL FEEDBACK INTEGRATION ==========

class VisualFeedbackManager:
    """Central manager for all visual feedback enhancements"""

    def __init__(self):
        self.enhanced_editors = {}
        self.operation_managers = {}
        self.validation_widgets = {}

    def enhance_piece_editor(self, piece_editor):
        """Enhance piece editor with visual feedback"""
        try:
            # Apply basic enhancements
            VisualFeedbackIntegrator.enhance_editor(
                piece_editor,
                enable_validation=True,
                enable_operations=True
            )

            # Add piece-specific validation rules
            validation_rules = create_validation_rules_for_piece_editor()
            VisualFeedbackIntegrator.add_validation_rules(piece_editor, validation_rules)

            # Setup data change tracking
            self._setup_piece_editor_tracking(piece_editor)

            # Enhance grid widgets if present
            self._enhance_piece_editor_grids(piece_editor)

            self.enhanced_editors['piece_editor'] = piece_editor
            logger.info("Piece editor enhanced with visual feedback")

        except Exception as e:
            logger.error(f"Error enhancing piece editor: {e}")

    def enhance_ability_editor(self, ability_editor):
        """Enhance ability editor with visual feedback"""
        try:
            # Apply basic enhancements
            VisualFeedbackIntegrator.enhance_editor(
                ability_editor,
                enable_validation=True,
                enable_operations=True
            )

            # Add ability-specific validation rules
            validation_rules = create_validation_rules_for_ability_editor()
            VisualFeedbackIntegrator.add_validation_rules(ability_editor, validation_rules)

            # Setup data change tracking
            self._setup_ability_editor_tracking(ability_editor)

            # Enhance grid widgets if present
            self._enhance_ability_editor_grids(ability_editor)

            self.enhanced_editors['ability_editor'] = ability_editor
            logger.info("Ability editor enhanced with visual feedback")

        except Exception as e:
            logger.error(f"Error enhancing ability editor: {e}")

    def _setup_piece_editor_tracking(self, piece_editor):
        """Setup data change tracking for piece editor"""
        if hasattr(piece_editor, 'validation_widget'):
            # Connect form field changes to validation
            self._connect_piece_editor_signals(piece_editor)

    def _setup_ability_editor_tracking(self, ability_editor):
        """Setup data change tracking for ability editor"""
        if hasattr(ability_editor, 'validation_widget'):
            # Connect form field changes to validation
            self._connect_ability_editor_signals(ability_editor)

    def _connect_piece_editor_signals(self, piece_editor):
        """Connect piece editor signals to validation"""
        try:
            # Connect to common piece editor fields
            if hasattr(piece_editor, 'name_edit'):
                piece_editor.name_edit.textChanged.connect(
                    lambda: self._trigger_validation(piece_editor)
                )

            if hasattr(piece_editor, 'description_edit'):
                piece_editor.description_edit.textChanged.connect(
                    lambda: self._trigger_validation(piece_editor)
                )

            if hasattr(piece_editor, 'value_spin'):
                piece_editor.value_spin.valueChanged.connect(
                    lambda: self._trigger_validation(piece_editor)
                )

        except Exception as e:
            logger.error(f"Error connecting piece editor signals: {e}")

    def _connect_ability_editor_signals(self, ability_editor):
        """Connect ability editor signals to validation"""
        try:
            # Connect to common ability editor fields
            if hasattr(ability_editor, 'name_edit'):
                ability_editor.name_edit.textChanged.connect(
                    lambda: self._trigger_validation(ability_editor)
                )

            if hasattr(ability_editor, 'cost_spin'):
                ability_editor.cost_spin.valueChanged.connect(
                    lambda: self._trigger_validation(ability_editor)
                )

        except Exception as e:
            logger.error(f"Error connecting ability editor signals: {e}")

    def _trigger_validation(self, editor):
        """Trigger validation for an editor"""
        if hasattr(editor, 'validation_widget'):
            # Use a timer to debounce rapid changes
            if not hasattr(editor, '_validation_timer'):
                editor._validation_timer = QTimer()
                editor._validation_timer.setSingleShot(True)
                editor._validation_timer.timeout.connect(
                    lambda: editor.validation_widget.validate_all()
                )

            editor._validation_timer.start(300)  # 300ms delay

    def _enhance_piece_editor_grids(self, piece_editor):
        """Enhance grid widgets in piece editor"""
        try:
            # Look for grid widgets and enhance them
            grid_widgets = self._find_grid_widgets(piece_editor)
            for grid_widget in grid_widgets:
                EnhancedGridVisualizer.enhance_grid_widget(grid_widget)

        except Exception as e:
            logger.error(f"Error enhancing piece editor grids: {e}")

    def _enhance_ability_editor_grids(self, ability_editor):
        """Enhance grid widgets in ability editor"""
        try:
            # Look for grid widgets and enhance them
            grid_widgets = self._find_grid_widgets(ability_editor)
            for grid_widget in grid_widgets:
                EnhancedGridVisualizer.enhance_grid_widget(grid_widget)

        except Exception as e:
            logger.error(f"Error enhancing ability editor grids: {e}")

    def _find_grid_widgets(self, parent_widget):
        """Find all grid widgets in a parent widget"""
        grid_widgets = []

        def search_children(widget):
            for child in widget.findChildren(QWidget):
                # Look for widgets that might be grids
                if hasattr(child, 'grid_size') or 'grid' in child.objectName().lower():
                    grid_widgets.append(child)
                search_children(child)

        search_children(parent_widget)
        return grid_widgets

    def enhance_main_window(self, main_window):
        """Enhance main window with visual feedback"""
        try:
            # Enhance status bar
            if hasattr(main_window, 'statusBar'):
                StatusBarEnhancement.enhance_status_bar(main_window.statusBar())

            # Add operation feedback manager
            operation_manager = OperationFeedbackManager(main_window)
            self.operation_managers['main_window'] = operation_manager

            logger.info("Main window enhanced with visual feedback")

        except Exception as e:
            logger.error(f"Error enhancing main window: {e}")

    def get_enhanced_editor(self, editor_type: str):
        """Get an enhanced editor by type"""
        return self.enhanced_editors.get(editor_type)

    def get_operation_manager(self, window_name: str):
        """Get operation manager for a window"""
        return self.operation_managers.get(window_name)

    def cleanup(self):
        """Cleanup all visual feedback enhancements"""
        try:
            # Cleanup operation managers
            for manager in self.operation_managers.values():
                if hasattr(manager, 'cleanup'):
                    manager.cleanup()

            # Clear references
            self.enhanced_editors.clear()
            self.operation_managers.clear()
            self.validation_widgets.clear()

            logger.info("Visual feedback manager cleaned up")

        except Exception as e:
            logger.error(f"Error during visual feedback cleanup: {e}")


# Global visual feedback manager instance
_visual_feedback_manager: Optional[VisualFeedbackManager] = None

def get_visual_feedback_manager() -> VisualFeedbackManager:
    """Get or create the global visual feedback manager"""
    global _visual_feedback_manager
    if _visual_feedback_manager is None:
        _visual_feedback_manager = VisualFeedbackManager()
    return _visual_feedback_manager

def reset_visual_feedback_manager():
    """Reset the global visual feedback manager (for testing)"""
    global _visual_feedback_manager
    if _visual_feedback_manager:
        _visual_feedback_manager.cleanup()
    _visual_feedback_manager = None


def apply_visual_feedback_to_editor(editor, editor_type: str = "piece"):
    """
    Apply visual feedback enhancements to an editor

    Args:
        editor: The editor instance to enhance
        editor_type: Type of editor ("piece" or "ability")
    """
    try:
        manager = get_visual_feedback_manager()

        if editor_type.lower() == "piece":
            manager.enhance_piece_editor(editor)
        elif editor_type.lower() == "ability":
            manager.enhance_ability_editor(editor)
        else:
            logger.warning(f"Unknown editor type: {editor_type}")

    except Exception as e:
        logger.error(f"Error applying visual feedback to editor: {e}")


def apply_visual_feedback_to_main_window(main_window):
    """
    Apply visual feedback enhancements to main window

    Args:
        main_window: The main window instance to enhance
    """
    try:
        manager = get_visual_feedback_manager()
        manager.enhance_main_window(main_window)

    except Exception as e:
        logger.error(f"Error applying visual feedback to main window: {e}")
